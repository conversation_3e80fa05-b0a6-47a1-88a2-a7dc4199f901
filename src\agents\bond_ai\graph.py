"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

from langgraph.graph import StateGraph, END

from langchain_core.messages import AIMessage

from bond_ai.configuration import Configuration
from bond_ai.state import Agent<PERSON><PERSON>

from bond_ai.nodes import  tool_node, call_model, table_indexing_node, planner_agent


def should_continue(state: AgentState):
    """Determine whether to continue with tool execution or end the conversation."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If we're in chat mode, always end (no tools should be used)
    is_chat_mode = state.get("mode") == "chat"
    if is_chat_mode:
        return "end"
    
    # If there are tool calls, continue to the tool node
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        return "continue"
    
    # Otherwise, end the conversation
    return "end"

# Configuration flag to control graph behavior
TEST_MODE = True  
# Define the graph
def create_graph():
    """Create and return the ReAct agent graph."""
 
 
    workflow = StateGraph(AgentState, config_schema=Configuration)
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.set_entry_point("table_indexing")
    workflow.add_node("planner", planner_agent)
    workflow.add_edge("table_indexing", "planner")


    if TEST_MODE:
        workflow.add_edge("planner", END)    
        graph = workflow.compile()
    else:
        
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)
        workflow.add_edge("planner", "agent")
        workflow.add_conditional_edges(
            "agent",
            should_continue,
            {
                "continue": "tools",
                "end": END,
            },
        )
        workflow.add_edge("tools", "agent")  
        graph = workflow.compile()
    
    graph.name = "Bond AI"  # Custom name for LangSmith
        
    return graph


# Create the graph
graph = create_graph()
