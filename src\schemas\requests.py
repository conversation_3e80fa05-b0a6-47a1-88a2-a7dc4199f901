from typing import Optional, Union, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, HttpUrl
from src.db.utils import Cell, CellId
from src.schemas.linkedin import LinkedinPersonProfileUrl, LinkedinCompanyProfileUrl
from src.services.http.models import HTTPRequest


class ValidateColumnsInput(BaseModel):
    table_id: str


class LinkedinProfileRequest(BaseModel):
    linkedin_profile_url: LinkedinPersonProfileUrl


class LookupPersonProfileRequest(BaseModel):
    company_domain: str
    full_name: str


class LinkedinCompanyRequest(BaseModel):
    linkedin_company_url: LinkedinCompanyProfileUrl


class LookupCompanyRequest(BaseModel):
    company_domain: str


class AIColumnRequest(BaseModel):
    user_prompt: str
    system_prompt: Optional[str | None] = None


class MobileFinderRequest(BaseModel):
    linkedin_profile_url: LinkedinPersonProfileUrl


class OutbondInvitationRequest(BaseModel):
    email: str
    display_name: str


class ServiceRequest(CellId):
    organization_id: str
    run_id: int
    service_id: int
    credits: int
    formula: Optional[str] = None
    providers: Optional[Dict[str, Any]] = None
    value: Union[
        LinkedinProfileRequest,
        LookupPersonProfileRequest,
        LinkedinCompanyRequest,
        LookupCompanyRequest,
        AIColumnRequest,
        MobileFinderRequest,
        OutbondInvitationRequest,
        HTTPRequest,
    ]


class ImportCSVInput(BaseModel):
    organization_id: str
    path: str


class ExportTableCSVInput(BaseModel):
    table_id: str
    organization_id: str
    download_id: int
    filters: Optional[Dict[str, Any]] = None
    search: Optional[str] = None
    sorts: Optional[list] = None


class NotifyFEInput(BaseModel):
    cell: Cell
