# Supervisor Agentic Pattern for Outbond AI Assistant

## Overview

This document defines a comprehensive Supervisor Agentic Pattern implementation for the Outbond AI Assistant using LangGraph. The pattern orchestrates specialized ReAct agents through a central Supervisor Agent that handles task planning, delegation, and result aggregation while maintaining full compatibility with the existing 16-tool ecosystem.

## Architecture Design

### Core Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Table Indexing │───▶│ Supervisor Agent │───▶│ Planner Node    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Task Delegation │───▶│ Execution Flow  │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌─────────────────────────────────────────────┐
                    │           Specialized Agents                │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐      │
                    │  │Research │ │Enrichmt │ │Content  │ ...  │
                    │  │ Agent   │ │ Agent   │ │ Agent   │      │
                    │  └─────────┘ └─────────┘ └─────────┘      │
                    └─────────────────────────────────────────────┘
```

## 1. Supervisor Agent System Prompt

### Core Supervisor Prompt

```python
SUPERVISOR_AGENT_PROMPT = """You are the Supervisor Agent for the Outbond AI Assistant, an intelligent orchestrator responsible for coordinating specialized ReAct agents to accomplish complex outbound sales and data enrichment tasks.

**ROLE & RESPONSIBILITIES:**

As the Supervisor Agent, you:
1. **Analyze** user requests to understand intent, scope, and complexity
2. **Plan** multi-step workflows by breaking down complex tasks into manageable subtasks
3. **Delegate** tasks to appropriate specialized agents based on their expertise
4. **Coordinate** inter-agent communication and data flow
5. **Monitor** task execution progress and handle errors/retries
6. **Aggregate** results from multiple agents into coherent responses
7. **Ensure** user confirmations are obtained for resource-intensive operations

**DECISION-MAKING CRITERIA:**

Task Delegation Rules:
- **Research Agent**: Web search, website scraping, LinkedIn profile discovery, competitive analysis
- **Enrichment Agent**: Contact data discovery (emails, phones), LinkedIn profile imports, data validation
- **Content Agent**: AI text generation, research insights, personalized messaging, copywriting
- **Data Management Agent**: Table operations, filtering, data analysis, column management
- **Execution Agent**: Column execution, monitoring, batch operations, result tracking

**COMMUNICATION PROTOCOLS:**

Inter-Agent Communication:
1. **Task Assignment**: Provide clear task descriptions with context and expected outputs
2. **Data Passing**: Use structured data formats for seamless information flow
3. **Status Updates**: Monitor agent progress and provide user feedback
4. **Error Handling**: Implement graceful fallbacks and retry mechanisms
5. **Result Integration**: Combine outputs from multiple agents coherently

**WORKFLOW ORCHESTRATION:**

Planning Process:
1. Parse user intent and identify required capabilities
2. Create detailed execution plan with task dependencies
3. Assign tasks to appropriate specialized agents
4. Monitor execution and handle inter-task dependencies
5. Aggregate results and provide comprehensive responses

**CONTEXT AWARENESS:**

Current Session Context:
- Table ID: {table_id}
- Current Date: {today_date}
- Active Filters: {current_filters}
- Table Summary: {table_summary}
- Selected Rows: {selected_row_ids}
- Selected Columns: {selected_column_ids}
- Mode: {mode}

**OPERATIONAL CONSTRAINTS:**

1. **Resource Management**: Always confirm resource-intensive operations with users
2. **Data Integrity**: Validate all data operations before execution
3. **Error Recovery**: Implement robust error handling with clear user communication
4. **Performance**: Optimize task delegation to minimize execution time
5. **User Experience**: Maintain clear communication throughout complex workflows

**DELEGATION SYNTAX:**

When delegating tasks, use this format:
```
DELEGATE_TO: [agent_name]
TASK: [clear task description]
CONTEXT: [relevant context and data]
EXPECTED_OUTPUT: [what the agent should return]
PRIORITY: [high/medium/low]
```

**AVAILABLE SPECIALIZED AGENTS:**
- research_agent: Web research and LinkedIn discovery
- enrichment_agent: Data enrichment and contact discovery  
- content_agent: AI content generation and copywriting
- data_management_agent: Table operations and data analysis
- execution_agent: Column execution and monitoring

Always maintain awareness of the current table state and user context when making delegation decisions."""
```

## 2. Specialized ReAct Agent Definitions

### Research Agent

**Tools**: `search`, `scrape_website`, `search_linkedin_profiles`

```python
RESEARCH_AGENT_PROMPT = """You are the Research Agent, a specialized ReAct agent focused on information discovery and competitive intelligence for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Web search for company information, market research, and competitive analysis
- Website content extraction for detailed company insights
- LinkedIn profile discovery and bulk prospect identification
- Industry trend analysis and firmographic research

**AVAILABLE TOOLS:**
1. **search**: General web search using Tavily for current information and research
2. **scrape_website**: Extract detailed content from specific websites in markdown format
3. **search_linkedin_profiles**: Bulk LinkedIn profile discovery with advanced filtering

**OPERATIONAL GUIDELINES:**
- Always start with broad research before narrowing to specific targets
- Use web search for company background and industry context
- Leverage website scraping for detailed company information
- Apply LinkedIn search for prospect identification with precise filtering
- Provide structured, actionable research insights

**RESEARCH METHODOLOGY:**
1. **Company Research**: Use search + scrape_website for comprehensive company analysis
2. **Market Analysis**: Leverage search for industry trends and competitive landscape
3. **Prospect Discovery**: Use search_linkedin_profiles with targeted filters
4. **Validation**: Cross-reference information across multiple sources

**OUTPUT FORMAT:**
Always provide research results in structured format with:
- Source URLs and credibility assessment
- Key findings and actionable insights
- Recommended next steps for enrichment or outreach
- Data quality and completeness indicators

**CONTEXT AWARENESS:**
- Respect user's current table filters and search criteria
- Align research scope with user's ICP (Ideal Customer Profile)
- Consider geographic and industry constraints
- Maintain focus on actionable sales intelligence

You excel at transforming raw information into strategic sales insights."""
```

### Enrichment Agent

**Tools**: `upsert_linkedin_person_profile_column_from_url`, `upsert_linkedin_company_profile_column_from_url`, `upsert_phone_number_column`, `upsert_work_email_column`

```python
ENRICHMENT_AGENT_PROMPT = """You are the Enrichment Agent, a specialized ReAct agent dedicated to data enrichment and contact discovery for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- LinkedIn profile data import and structuring
- Contact information discovery (emails and phone numbers)
- Data validation and quality assurance
- Profile completeness optimization

**AVAILABLE TOOLS:**
1. **upsert_linkedin_person_profile_column_from_url**: Import LinkedIn person profiles with comprehensive data extraction
2. **upsert_linkedin_company_profile_column_from_url**: Import LinkedIn company profiles for firmographic data
3. **upsert_phone_number_column**: Discover phone numbers from LinkedIn profiles using multiple providers
4. **upsert_work_email_column**: Find work emails using name and company domain with verification

**ENRICHMENT STRATEGY:**
1. **Profile Import**: Start with LinkedIn profile imports for foundational data
2. **Contact Discovery**: Layer email and phone discovery on top of profile data
3. **Data Validation**: Ensure data quality and completeness
4. **Progressive Enhancement**: Build rich prospect profiles incrementally

**DATA QUALITY STANDARDS:**
- Validate injection paths before column creation
- Use appropriate provider combinations for maximum coverage
- Implement data verification where available
- Handle missing or incomplete data gracefully

**COLUMN MANAGEMENT:**
- Always check for existing columns before creating new ones
- Use column_id parameter when updating existing columns
- Follow naming conventions for consistency
- Optimize injection paths for data accuracy

**PROVIDER OPTIMIZATION:**
- Email Discovery: leadmagic, findymail, prospeo + millionverifier for verification
- Phone Discovery: leadmagic, prospeo for maximum coverage
- LinkedIn Data: outbond provider for comprehensive profile extraction

**ERROR HANDLING:**
- Gracefully handle missing LinkedIn URLs or invalid profiles
- Provide clear feedback on data quality issues
- Suggest alternative enrichment strategies when primary methods fail
- Maintain data integrity throughout the enrichment process

You specialize in transforming basic prospect information into rich, actionable contact profiles."""
```

### Content Agent

**Tools**: `upsert_text_column`, `upsert_ai_text_column`, `upsert_bond_ai_researcher_column`, `upsert_ai_message_copywriter`

```python
CONTENT_AGENT_PROMPT = """You are the Content Agent, a specialized ReAct agent focused on AI-powered content generation and personalized messaging for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- AI-powered text generation for various sales contexts
- Personalized message creation using prospect data
- Research insight generation for sales intelligence
- Content optimization for engagement and conversion

**AVAILABLE TOOLS:**
1. **upsert_text_column**: Create static text or formula-based columns for consistent messaging
2. **upsert_ai_text_column**: Generate AI-powered content with custom prompts and injection paths
3. **upsert_bond_ai_researcher_column**: Create detailed prospect research insights and intelligence
4. **upsert_ai_message_copywriter**: Generate personalized outreach messages optimized for engagement

**CONTENT STRATEGY:**
1. **Research-Driven**: Use prospect data to inform content personalization
2. **Context-Aware**: Leverage company and industry information for relevance
3. **Engagement-Focused**: Optimize messaging for response rates and conversion
4. **Scalable**: Create templates and formulas for efficient content generation

**PERSONALIZATION FRAMEWORK:**
- **Level 1**: Basic personalization (name, company, title)
- **Level 2**: Role-based messaging (industry, seniority, function)
- **Level 3**: Deep personalization (recent news, mutual connections, specific pain points)
- **Level 4**: Hyper-personalization (recent activities, company events, personal interests)

**CONTENT TYPES:**
- **Cold Outreach**: Initial contact messages for LinkedIn and email
- **Follow-up Sequences**: Multi-touch campaign messaging
- **Research Summaries**: Prospect intelligence and talking points
- **Value Propositions**: Customized benefit statements
- **Call-to-Actions**: Compelling next-step requests

**INJECTION PATH OPTIMIZATION:**
- Use precise injection paths for data accuracy
- Implement fallback content for missing data
- Validate required fields before content generation
- Optimize for readability and natural language flow

**QUALITY ASSURANCE:**
- Ensure content aligns with brand voice and messaging
- Validate personalization accuracy
- Test content variations for effectiveness
- Maintain professional tone and compliance standards

You excel at transforming prospect data into compelling, personalized content that drives engagement."""

### Data Management Agent

**Tools**: `read_table_data`, `read_user_view_table_filters`, `update_user_view_table_filters_tool`

```python
DATA_MANAGEMENT_AGENT_PROMPT = """You are the Data Management Agent, a specialized ReAct agent focused on table operations, data analysis, and information management for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Table data retrieval with advanced filtering and sorting
- View management and filter optimization
- Data analysis and insight generation
- Table structure understanding and optimization

**AVAILABLE TOOLS:**
1. **read_table_data**: Fetch table data with comprehensive filtering, sorting, and summarization
2. **read_user_view_table_filters**: Retrieve current table view filters and configurations
3. **update_user_view_table_filters_tool**: Update table filters for optimized data views

**DATA OPERATIONS STRATEGY:**
1. **Efficient Querying**: Use summarization by default, full data only when necessary
2. **Filter Optimization**: Align with user's current view and preferences
3. **Performance Focus**: Minimize data transfer while maximizing insight value
4. **Context Preservation**: Maintain user's table state and selections

**FILTERING EXPERTISE:**
- Complex filter combinations with AND/OR logic
- Column-specific operators (eq, neq, contains, empty, etc.)
- Status-based filtering (running, completed, failed, awaiting_input)
- Data quality filtering (error, result, empty states)

**ANALYSIS CAPABILITIES:**
- Data completeness assessment
- Quality metrics and validation
- Trend identification and pattern recognition
- Performance optimization recommendations

**OPERATIONAL GUIDELINES:**
- Always respect user's current table filters unless explicitly asked to change
- Use targeted column selection for efficiency
- Provide data insights along with raw information
- Maintain awareness of table structure and relationships

You excel at transforming raw table data into actionable business intelligence."""
```

### Execution Agent

**Tools**: `run_column`

```python
EXECUTION_AGENT_PROMPT = """You are the Execution Agent, a specialized ReAct agent dedicated to column execution, monitoring, and batch operations for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Smart column execution with user confirmation
- Batch operation management and monitoring
- Execution status tracking and reporting
- Performance optimization and error recovery

**AVAILABLE TOOLS:**
1. **run_column**: Execute smart columns with comprehensive monitoring and confirmation workflows

**EXECUTION STRATEGY:**
1. **Pre-execution Validation**: Verify column runnability and data requirements
2. **User Confirmation**: Obtain explicit approval for resource-intensive operations
3. **Monitoring**: Track execution progress and handle status updates
4. **Error Recovery**: Implement retry logic and fallback strategies

**CONFIRMATION PROTOCOLS:**
- Always confirm before executing columns that process multiple rows
- Provide clear information about scope (column name, row count)
- Handle user cancellations gracefully
- Communicate execution progress and results

**MONITORING CAPABILITIES:**
- Real-time execution status tracking
- Completion rate monitoring
- Error detection and reporting
- Performance metrics collection

**BATCH OPERATION MANAGEMENT:**
- Optimize execution order for efficiency
- Handle dependencies between column executions
- Manage resource allocation and throttling
- Provide comprehensive execution summaries

**ERROR HANDLING:**
- Graceful handling of execution failures
- Detailed error reporting with actionable insights
- Automatic retry logic with exponential backoff
- Fallback strategies for critical operations

You specialize in reliable, efficient execution of data processing operations with comprehensive monitoring and user communication."""
```
```

## 3. Planner Node Implementation

### Planning Architecture

```python
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum

class TaskType(str, Enum):
    RESEARCH = "research"
    ENRICHMENT = "enrichment" 
    CONTENT = "content"
    DATA_MANAGEMENT = "data_management"
    EXECUTION = "execution"

class TaskPriority(str, Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Task(BaseModel):
    id: str = Field(..., description="Unique task identifier")
    type: TaskType = Field(..., description="Task category")
    agent: str = Field(..., description="Assigned agent name")
    description: str = Field(..., description="Detailed task description")
    tools: List[str] = Field(..., description="Required tools for task")
    dependencies: List[str] = Field(default=[], description="Task IDs this task depends on")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    context: Dict[str, Any] = Field(default={}, description="Task-specific context")
    expected_output: str = Field(..., description="Expected output format")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    max_retries: int = Field(default=3, description="Maximum retry attempts")

class ExecutionPlan(BaseModel):
    id: str = Field(..., description="Unique plan identifier")
    user_request: str = Field(..., description="Original user request")
    tasks: List[Task] = Field(..., description="Ordered list of tasks")
    estimated_duration: int = Field(..., description="Estimated execution time in seconds")
    success_criteria: List[str] = Field(..., description="Success criteria for plan completion")
    fallback_strategies: List[str] = Field(default=[], description="Fallback options if plan fails")

def planner_node(state: AgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Advanced planning node that creates detailed execution plans for complex workflows.

    This node analyzes user requests and creates comprehensive execution plans with:
    - Task breakdown and dependency mapping
    - Agent assignment based on tool requirements
    - Error handling and retry strategies
    - Success criteria and validation checkpoints
    """

    # Extract user request from messages
    messages = state.get("messages", [])
    if not messages:
        return {"execution_plan": None, "error": "No user request found"}

    # Get the latest human message
    user_request = None
    for message in reversed(messages):
        if hasattr(message, 'type') and message.type == 'human':
            user_request = message.content
            break

    if not user_request:
        return {"execution_plan": None, "error": "No user request found in messages"}

    # Load planning model
    configuration = Configuration.from_runnable_config(config)
    model = load_chat_model(configuration.model)

    # Create planning prompt
    planning_prompt = f"""
    Analyze the following user request and create a detailed execution plan:

    USER REQUEST: {user_request}

    CONTEXT:
    - Table Summary: {state.get('table_summary', 'Not available')}
    - Current Mode: {state.get('mode', 'tool')}
    - Selected Rows: {state.get('selected_row_ids', 'None')}
    - Selected Columns: {state.get('selected_column_ids', 'None')}

    AVAILABLE AGENTS AND TOOLS:

    Research Agent:
    - search: Web search for information and research
    - scrape_website: Extract content from websites
    - search_linkedin_profiles: Bulk LinkedIn profile discovery

    Enrichment Agent:
    - upsert_linkedin_person_profile_column_from_url: Import LinkedIn person profiles
    - upsert_linkedin_company_profile_column_from_url: Import LinkedIn company profiles
    - upsert_phone_number_column: Discover phone numbers
    - upsert_work_email_column: Find work emails

    Content Agent:
    - upsert_text_column: Create text/formula columns
    - upsert_ai_text_column: AI-generated content
    - upsert_bond_ai_researcher_column: Research insights
    - upsert_ai_message_copywriter: Personalized messaging

    Data Management Agent:
    - read_table_data: Fetch table data with filtering
    - read_user_view_table_filters: Get current filters
    - update_user_view_table_filters_tool: Update table filters

    Execution Agent:
    - run_column: Execute smart columns with monitoring

    Create a JSON execution plan with the following structure:
    {{
        "id": "unique_plan_id",
        "user_request": "original request",
        "tasks": [
            {{
                "id": "task_1",
                "type": "research|enrichment|content|data_management|execution",
                "agent": "agent_name",
                "description": "detailed task description",
                "tools": ["tool1", "tool2"],
                "dependencies": ["task_id_if_any"],
                "priority": "high|medium|low",
                "context": {{"key": "value"}},
                "expected_output": "what this task should produce"
            }}
        ],
        "estimated_duration": 120,
        "success_criteria": ["criterion1", "criterion2"],
        "fallback_strategies": ["strategy1", "strategy2"]
    }}

    PLANNING GUIDELINES:
    1. Break complex requests into logical, sequential tasks
    2. Assign tasks to agents based on their tool capabilities
    3. Define clear dependencies between tasks
    4. Include validation and error handling steps
    5. Estimate realistic execution times
    6. Provide fallback strategies for critical failures
    """

    try:
        # Get planning response from model
        planning_response = model.invoke([HumanMessage(planning_prompt)], config)

        # Parse the JSON response
        import json
        plan_data = json.loads(planning_response.content)

        # Validate and create ExecutionPlan object
        execution_plan = ExecutionPlan(**plan_data)

        return {
            "execution_plan": execution_plan.model_dump(),
            "messages": [AIMessage(f"Created execution plan with {len(execution_plan.tasks)} tasks")]
        }

    except Exception as e:
        return {
            "execution_plan": None,
            "error": f"Planning failed: {str(e)}",
            "messages": [AIMessage(f"Planning failed: {str(e)}")]
        }
```

## 4. Complete Feature Coverage - Tool Mapping

### Tool Distribution Across Specialized Agents

```python
# Complete tool mapping for specialized agents
AGENT_TOOL_MAPPING = {
    "research_agent": [
        "search",                           # Web search for research
        "scrape_website",                   # Website content extraction
        "search_linkedin_profiles"          # LinkedIn profile discovery
    ],

    "enrichment_agent": [
        "upsert_linkedin_person_profile_column_from_url",    # LinkedIn person import
        "upsert_linkedin_company_profile_column_from_url",   # LinkedIn company import
        "upsert_phone_number_column",                        # Phone discovery
        "upsert_work_email_column"                           # Email discovery
    ],

    "content_agent": [
        "upsert_text_column",               # Static text/formula columns
        "upsert_ai_text_column",            # AI-generated content
        "upsert_bond_ai_researcher_column", # Research insights
        "upsert_ai_message_copywriter"      # Personalized messaging
    ],

    "data_management_agent": [
        "read_table_data",                  # Table data retrieval
        "read_user_view_table_filters",     # Filter management
        "update_user_view_table_filters_tool" # Filter updates
    ],

    "execution_agent": [
        "run_column"                        # Column execution and monitoring
    ]
}

# Tool binding for each specialized agent
def create_specialized_agents(tools_by_name: Dict[str, Any]) -> Dict[str, Any]:
    """Create specialized agents with their respective tool bindings."""

    agents = {}

    # Research Agent
    research_tools = [tools_by_name[tool] for tool in AGENT_TOOL_MAPPING["research_agent"]]
    research_model = load_chat_model(configuration.model).bind_tools(research_tools)
    agents["research_agent"] = {
        "model": research_model,
        "prompt": RESEARCH_AGENT_PROMPT,
        "tools": research_tools
    }

    # Enrichment Agent
    enrichment_tools = [tools_by_name[tool] for tool in AGENT_TOOL_MAPPING["enrichment_agent"]]
    enrichment_model = load_chat_model(configuration.model).bind_tools(enrichment_tools)
    agents["enrichment_agent"] = {
        "model": enrichment_model,
        "prompt": ENRICHMENT_AGENT_PROMPT,
        "tools": enrichment_tools
    }

    # Content Agent
    content_tools = [tools_by_name[tool] for tool in AGENT_TOOL_MAPPING["content_agent"]]
    content_model = load_chat_model(configuration.model).bind_tools(content_tools)
    agents["content_agent"] = {
        "model": content_model,
        "prompt": CONTENT_AGENT_PROMPT,
        "tools": content_tools
    }

    # Data Management Agent
    data_tools = [tools_by_name[tool] for tool in AGENT_TOOL_MAPPING["data_management_agent"]]
    data_model = load_chat_model(configuration.model).bind_tools(data_tools)
    agents["data_management_agent"] = {
        "model": data_model,
        "prompt": DATA_MANAGEMENT_AGENT_PROMPT,
        "tools": data_tools
    }

    # Execution Agent
    execution_tools = [tools_by_name[tool] for tool in AGENT_TOOL_MAPPING["execution_agent"]]
    execution_model = load_chat_model(configuration.model).bind_tools(execution_tools)
    agents["execution_agent"] = {
        "model": execution_model,
        "prompt": EXECUTION_AGENT_PROMPT,
        "tools": execution_tools
    }

    return agents
```

### Use Case Coverage Validation

```python
# Comprehensive use case mapping to ensure all current functionality is preserved
USE_CASE_COVERAGE = {
    "prospect_research": {
        "primary_agent": "research_agent",
        "tools": ["search", "scrape_website", "search_linkedin_profiles"],
        "workflow": "Research → Discovery → Validation"
    },

    "data_enrichment": {
        "primary_agent": "enrichment_agent",
        "tools": ["upsert_linkedin_person_profile_column_from_url",
                 "upsert_phone_number_column", "upsert_work_email_column"],
        "workflow": "Profile Import → Contact Discovery → Validation"
    },

    "content_generation": {
        "primary_agent": "content_agent",
        "tools": ["upsert_ai_text_column", "upsert_bond_ai_researcher_column",
                 "upsert_ai_message_copywriter"],
        "workflow": "Research → Content Creation → Personalization"
    },

    "table_management": {
        "primary_agent": "data_management_agent",
        "tools": ["read_table_data", "read_user_view_table_filters",
                 "update_user_view_table_filters_tool"],
        "workflow": "Analysis → Filtering → Optimization"
    },

    "execution_monitoring": {
        "primary_agent": "execution_agent",
        "tools": ["run_column"],
        "workflow": "Validation → Execution → Monitoring → Reporting"
    }
}
```

## 5. LangGraph Implementation

### Enhanced AgentState for Supervisor Pattern

```python
from typing import Annotated, Sequence, TypedDict, Optional, List, Dict, Any
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages

def preserve_table_summary(left: Optional[str], right: Optional[str]) -> Optional[str]:
    """Preserve table_summary, prioritizing the most recent non-None value."""
    if right is not None:
        return right
    return left

def preserve_execution_plan(left: Optional[Dict], right: Optional[Dict]) -> Optional[Dict]:
    """Preserve execution plan, prioritizing the most recent non-None value."""
    if right is not None:
        return right
    return left

def update_task_status(left: Optional[List], right: Optional[List]) -> Optional[List]:
    """Update task status list, merging by task ID."""
    if not left and not right:
        return None
    if not left:
        return right
    if not right:
        return left

    # Merge task lists by ID
    task_dict = {task.get('id'): task for task in left}
    for task in right:
        task_dict[task.get('id')] = task

    return list(task_dict.values())

class SupervisorAgentState(TypedDict):
    """Enhanced state structure for supervisor agentic pattern."""

    # Core message handling (preserved from original)
    messages: Annotated[Sequence[BaseMessage], add_messages]

    # Table context (preserved from original)
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]

    # Supervisor-specific state
    execution_plan: Annotated[Optional[Dict], preserve_execution_plan]
    current_task_id: Optional[str]
    task_status: Annotated[Optional[List], update_task_status]
    active_agent: Optional[str]
    delegation_history: Optional[List[Dict]]

    # Inter-agent communication
    agent_outputs: Optional[Dict[str, Any]]
    pending_confirmations: Optional[List[Dict]]
    error_context: Optional[Dict[str, Any]]
```

### Complete LangGraph Workflow Definition

```python
from langgraph.graph import StateGraph, END, START
from langgraph.types import interrupt
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage

def supervisor_node(state: SupervisorAgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Supervisor node that orchestrates task delegation and monitors execution.
    """
    configuration = Configuration.from_runnable_config(config)

    # Check if we have an execution plan
    execution_plan = state.get("execution_plan")
    if not execution_plan:
        # No plan exists, delegate to planner
        return {
            "messages": [AIMessage("Creating execution plan...")],
            "active_agent": "planner"
        }

    # Check current task status
    current_task_id = state.get("current_task_id")
    task_status = state.get("task_status", [])

    # Find next task to execute
    next_task = None
    for task in execution_plan.get("tasks", []):
        task_id = task.get("id")

        # Check if task is pending and dependencies are met
        if task.get("status") == "pending":
            dependencies = task.get("dependencies", [])
            dependencies_met = all(
                any(t.get("id") == dep_id and t.get("status") == "completed"
                    for t in task_status)
                for dep_id in dependencies
            ) if dependencies else True

            if dependencies_met:
                next_task = task
                break

    if not next_task:
        # All tasks completed or no executable tasks
        return {
            "messages": [AIMessage("All tasks completed successfully!")],
            "active_agent": None
        }

    # Delegate to appropriate agent
    agent_name = next_task.get("agent")
    task_description = next_task.get("description")

    # Update task status to in_progress
    updated_status = task_status.copy() if task_status else []
    for task in updated_status:
        if task.get("id") == next_task.get("id"):
            task["status"] = "in_progress"
            break
    else:
        updated_status.append({
            "id": next_task.get("id"),
            "status": "in_progress",
            "agent": agent_name
        })

    # Create delegation message
    delegation_message = f"""
    TASK DELEGATION:
    Agent: {agent_name}
    Task: {task_description}
    Tools: {next_task.get('tools', [])}
    Context: {next_task.get('context', {})}
    Expected Output: {next_task.get('expected_output', '')}
    """

    return {
        "messages": [AIMessage(delegation_message)],
        "current_task_id": next_task.get("id"),
        "task_status": updated_status,
        "active_agent": agent_name
    }

def specialized_agent_node(agent_name: str):
    """
    Factory function to create specialized agent nodes.
    """
    def agent_node(state: SupervisorAgentState, config: RunnableConfig) -> Dict[str, Any]:
        configuration = Configuration.from_runnable_config(config)

        # Get agent configuration
        agents = create_specialized_agents(tools_by_name)
        agent_config = agents.get(agent_name)

        if not agent_config:
            return {
                "messages": [AIMessage(f"Error: Agent {agent_name} not found")],
                "error_context": {"agent": agent_name, "error": "Agent not found"}
            }

        # Get current task context
        current_task_id = state.get("current_task_id")
        execution_plan = state.get("execution_plan", {})
        current_task = None

        for task in execution_plan.get("tasks", []):
            if task.get("id") == current_task_id:
                current_task = task
                break

        if not current_task:
            return {
                "messages": [AIMessage(f"Error: No current task found for {agent_name}")],
                "error_context": {"agent": agent_name, "error": "No current task"}
            }

        # Create agent-specific system prompt
        system_prompt = agent_config["prompt"].format(
            table_summary=state.get("table_summary", ""),
            current_task=current_task.get("description", ""),
            task_context=current_task.get("context", {}),
            expected_output=current_task.get("expected_output", "")
        )

        # Prepare messages for agent
        messages = [
            SystemMessage(system_prompt),
            *state.get("messages", [])[-3:]  # Last 3 messages for context
        ]

        try:
            # Call specialized agent
            model = agent_config["model"]
            response = model.invoke(messages, config)

            # Check if agent made tool calls
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # Execute tools
                tool_outputs = []
                for tool_call in response.tool_calls:
                    try:
                        tool = tools_by_name[tool_call["name"]]
                        tool_result = tool.invoke(tool_call["args"])
                        tool_outputs.append(ToolMessage(
                            content=str(tool_result),
                            name=tool_call["name"],
                            tool_call_id=tool_call["id"]
                        ))
                    except Exception as e:
                        tool_outputs.append(ToolMessage(
                            content=f"Error: {str(e)}",
                            name=tool_call["name"],
                            tool_call_id=tool_call["id"]
                        ))

                # Update task status to completed
                task_status = state.get("task_status", [])
                for task in task_status:
                    if task.get("id") == current_task_id:
                        task["status"] = "completed"
                        break

                return {
                    "messages": [response] + tool_outputs,
                    "task_status": task_status,
                    "agent_outputs": {agent_name: tool_outputs}
                }
            else:
                # Agent provided response without tools
                return {
                    "messages": [response],
                    "agent_outputs": {agent_name: response.content}
                }

        except Exception as e:
            # Handle agent execution error
            error_message = f"Agent {agent_name} execution failed: {str(e)}"

            # Update task status to failed
            task_status = state.get("task_status", [])
            for task in task_status:
                if task.get("id") == current_task_id:
                    task["status"] = "failed"
                    task["error"] = str(e)
                    break

            return {
                "messages": [AIMessage(error_message)],
                "task_status": task_status,
                "error_context": {"agent": agent_name, "error": str(e)}
            }

    return agent_node

def should_continue_supervisor(state: SupervisorAgentState) -> str:
    """
    Determine the next node in the supervisor workflow.
    """
    active_agent = state.get("active_agent")

    if active_agent == "planner":
        return "planner"
    elif active_agent in ["research_agent", "enrichment_agent", "content_agent",
                         "data_management_agent", "execution_agent"]:
        return active_agent
    elif active_agent is None:
        return END
    else:
        return "supervisor"

def create_supervisor_graph() -> StateGraph:
    """
    Create the complete supervisor agentic pattern graph.
    """
    # Create the graph with enhanced state
    workflow = StateGraph(SupervisorAgentState, config_schema=Configuration)

    # Add nodes
    workflow.add_node("table_indexing", table_indexing_node)  # Preserved from original
    workflow.add_node("supervisor", supervisor_node)
    workflow.add_node("planner", planner_node)

    # Add specialized agent nodes
    workflow.add_node("research_agent", specialized_agent_node("research_agent"))
    workflow.add_node("enrichment_agent", specialized_agent_node("enrichment_agent"))
    workflow.add_node("content_agent", specialized_agent_node("content_agent"))
    workflow.add_node("data_management_agent", specialized_agent_node("data_management_agent"))
    workflow.add_node("execution_agent", specialized_agent_node("execution_agent"))

    # Define workflow edges
    workflow.set_entry_point("table_indexing")
    workflow.add_edge("table_indexing", "supervisor")

    # Conditional edges from supervisor
    workflow.add_conditional_edges(
        "supervisor",
        should_continue_supervisor,
        {
            "planner": "planner",
            "research_agent": "research_agent",
            "enrichment_agent": "enrichment_agent",
            "content_agent": "content_agent",
            "data_management_agent": "data_management_agent",
            "execution_agent": "execution_agent",
            END: END
        }
    )

    # All specialized agents return to supervisor
    workflow.add_edge("planner", "supervisor")
    workflow.add_edge("research_agent", "supervisor")
    workflow.add_edge("enrichment_agent", "supervisor")
    workflow.add_edge("content_agent", "supervisor")
    workflow.add_edge("data_management_agent", "supervisor")
    workflow.add_edge("execution_agent", "supervisor")

    # Compile the graph
    graph = workflow.compile()
    graph.name = "Supervisor Agentic Pattern"

    return graph

# Create the supervisor graph instance
supervisor_graph = create_supervisor_graph()
```

## Sequence Diagrams - Supervisor-to-Agent Task Flows

### Scenario 1: Complex Prospect Research and Enrichment

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant Research
    participant Enrichment
    participant Content
    participant Execution

    User->>Supervisor: "Find 50 marketing managers at SaaS companies and create personalized outreach"
    Supervisor->>Planner: Create execution plan
    Planner-->>Supervisor: Plan with 5 tasks across 4 agents

    Supervisor->>Research: Task 1: Find LinkedIn profiles
    Research->>Research: search_linkedin_profiles
    Research-->>Supervisor: 50 profiles discovered

    Supervisor->>Enrichment: Task 2: Import LinkedIn profiles
    Enrichment->>Enrichment: upsert_linkedin_person_profile_column_from_url
    Enrichment-->>Supervisor: Profile column created

    Supervisor->>Enrichment: Task 3: Discover work emails
    Enrichment->>Enrichment: upsert_work_email_column
    Enrichment-->>Supervisor: Email column created

    Supervisor->>Content: Task 4: Create personalized messages
    Content->>Content: upsert_ai_message_copywriter
    Content-->>Supervisor: Message column created

    Supervisor->>Execution: Task 5: Execute all columns
    Execution->>User: Confirm execution for 50 rows?
    User-->>Execution: Confirmed
    Execution->>Execution: run_column (multiple)
    Execution-->>Supervisor: All columns executed successfully

    Supervisor->>User: "Campaign ready: 50 prospects with personalized messages"
```

### Scenario 2: Data Analysis and Filtering

```mermaid
sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant DataMgmt
    participant Research

    User->>Supervisor: "Show me prospects from companies with 100+ employees in tech industry"
    Supervisor->>Planner: Create analysis plan
    Planner-->>Supervisor: Plan with data filtering and research validation

    Supervisor->>DataMgmt: Task 1: Analyze current table state
    DataMgmt->>DataMgmt: read_user_view_table_filters
    DataMgmt->>DataMgmt: read_table_data (summary)
    DataMgmt-->>Supervisor: Current state analysis

    Supervisor->>DataMgmt: Task 2: Apply company size filters
    DataMgmt->>DataMgmt: update_user_view_table_filters_tool
    DataMgmt-->>Supervisor: Filters applied

    Supervisor->>Research: Task 3: Validate company information
    Research->>Research: search (company verification)
    Research-->>Supervisor: Company data validated

    Supervisor->>DataMgmt: Task 4: Generate filtered dataset
    DataMgmt->>DataMgmt: read_table_data (filtered)
    DataMgmt-->>Supervisor: Filtered results ready

    Supervisor->>User: "Found 23 prospects from tech companies 100+ employees"
```

## Backward Compatibility and Migration

### Preserving Existing Features

```python
# Compatibility layer for existing implementations
class CompatibilityLayer:
    """
    Ensures backward compatibility with existing single-agent implementation.
    """

    @staticmethod
    def migrate_existing_state(old_state: AgentState) -> SupervisorAgentState:
        """Migrate existing AgentState to SupervisorAgentState."""
        return SupervisorAgentState(
            messages=old_state.get("messages", []),
            table_summary=old_state.get("table_summary"),
            mode=old_state.get("mode"),
            selected_row_ids=old_state.get("selected_row_ids"),
            selected_column_ids=old_state.get("selected_column_ids"),
            # Initialize supervisor-specific fields
            execution_plan=None,
            current_task_id=None,
            task_status=None,
            active_agent=None,
            delegation_history=None,
            agent_outputs=None,
            pending_confirmations=None,
            error_context=None
        )

    @staticmethod
    def fallback_to_single_agent(state: SupervisorAgentState) -> bool:
        """
        Determine if request should fallback to single-agent mode.
        """
        # Simple requests that don't require multi-agent coordination
        messages = state.get("messages", [])
        if not messages:
            return True

        last_message = messages[-1]
        if hasattr(last_message, 'content'):
            content = last_message.content.lower()

            # Single-tool operations
            simple_patterns = [
                "read table", "show data", "what's in", "filter by",
                "run column", "execute", "search for", "scrape"
            ]

            if any(pattern in content for pattern in simple_patterns):
                return True

        return False

# Configuration flag for gradual migration
ENABLE_SUPERVISOR_PATTERN = True

def create_adaptive_graph() -> StateGraph:
    """
    Create graph that can operate in both single-agent and supervisor modes.
    """
    if ENABLE_SUPERVISOR_PATTERN:
        return create_supervisor_graph()
    else:
        # Fallback to original single-agent implementation
        return create_graph()  # Original implementation
```

### Error Handling and User Experience Preservation

```python
def preserve_user_confirmations(state: SupervisorAgentState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Ensure user confirmations are preserved across agent transitions.
    """
    pending_confirmations = state.get("pending_confirmations", [])

    if pending_confirmations:
        # Handle pending confirmations before proceeding
        for confirmation in pending_confirmations:
            if confirmation.get("type") == "run_column":
                # Use existing interrupt mechanism
                user_response = interrupt(confirmation.get("message"))

                if user_response and str(user_response).lower() in ['yes', 'y', 'confirm', 'proceed', 'ok', 'run']:
                    # User confirmed, proceed with execution
                    confirmation["status"] = "confirmed"
                else:
                    # User cancelled, update task status
                    confirmation["status"] = "cancelled"

    return {"pending_confirmations": pending_confirmations}

def maintain_stream_writing(agent_name: str, action: str) -> None:
    """
    Preserve real-time status updates across all agents.
    """
    from langgraph.config import get_stream_writer

    stream_writer = get_stream_writer()
    stream_writer({
        "custom_tool_call": f"[{agent_name}] {action}",
        "agent": agent_name,
        "timestamp": datetime.now().isoformat()
    })
```

## Performance Optimizations and Monitoring

### Execution Metrics

```python
class SupervisorMetrics:
    """
    Performance monitoring for supervisor agentic pattern.
    """

    def __init__(self):
        self.task_execution_times = {}
        self.agent_performance = {}
        self.error_rates = {}
        self.user_satisfaction = {}

    def track_task_execution(self, task_id: str, agent: str, duration: float, success: bool):
        """Track individual task performance."""
        if agent not in self.agent_performance:
            self.agent_performance[agent] = {
                "total_tasks": 0,
                "successful_tasks": 0,
                "average_duration": 0,
                "error_count": 0
            }

        metrics = self.agent_performance[agent]
        metrics["total_tasks"] += 1
        if success:
            metrics["successful_tasks"] += 1
        else:
            metrics["error_count"] += 1

        # Update average duration
        current_avg = metrics["average_duration"]
        total_tasks = metrics["total_tasks"]
        metrics["average_duration"] = ((current_avg * (total_tasks - 1)) + duration) / total_tasks

    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        return {
            "agent_performance": self.agent_performance,
            "total_tasks_executed": sum(m["total_tasks"] for m in self.agent_performance.values()),
            "overall_success_rate": sum(m["successful_tasks"] for m in self.agent_performance.values()) /
                                  max(sum(m["total_tasks"] for m in self.agent_performance.values()), 1),
            "average_execution_time": sum(m["average_duration"] for m in self.agent_performance.values()) /
                                    max(len(self.agent_performance), 1)
        }

# Global metrics instance
supervisor_metrics = SupervisorMetrics()
```

## Summary

This Supervisor Agentic Pattern implementation provides:

1. **Complete Tool Coverage**: All 16 existing tools mapped to specialized agents
2. **Backward Compatibility**: Seamless migration from single-agent to multi-agent pattern
3. **Enhanced Planning**: Sophisticated task breakdown and dependency management
4. **Robust Error Handling**: Comprehensive error recovery and user communication
5. **Performance Monitoring**: Detailed metrics and optimization capabilities
6. **User Experience Preservation**: All existing confirmations and real-time updates maintained

The pattern enables complex multi-step workflows while maintaining the simplicity and reliability of the original single-agent implementation for straightforward tasks.
```
```
```
```
