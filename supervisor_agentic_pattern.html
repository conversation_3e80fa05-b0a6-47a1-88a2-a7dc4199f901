<!DOCTYPE html>
<html>
<head>
<title>supervisor_agentic_pattern.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="supervisor-agentic-pattern-for-outbond-ai-assistant">Supervisor Agentic Pattern for Outbond AI Assistant</h1>
<h2 id="overview">Overview</h2>
<p>This document defines a comprehensive Supervisor Agentic Pattern implementation for the Outbond AI Assistant using LangGraph. The pattern orchestrates specialized ReAct agents through a central Supervisor Agent that handles task planning, delegation, and result aggregation while maintaining full compatibility with the existing 16-tool ecosystem.</p>
<h2 id="architecture-design">Architecture Design</h2>
<h3 id="core-components">Core Components</h3>
<pre class="hljs"><code><div>┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Table Indexing │───▶│ Supervisor Agent │───▶│ Planner Node    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Task Delegation │───▶│ Execution Flow  │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌─────────────────────────────────────────────┐
                    │           Specialized Agents                │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐      │
                    │  │Research │ │Enrichmt │ │Content  │ ...  │
                    │  │ Agent   │ │ Agent   │ │ Agent   │      │
                    │  └─────────┘ └─────────┘ └─────────┘      │
                    └─────────────────────────────────────────────┘
</div></code></pre>
<h2 id="1-supervisor-agent-system-prompt">1. Supervisor Agent System Prompt</h2>
<h3 id="core-supervisor-prompt">Core Supervisor Prompt</h3>
<pre class="hljs"><code><div>SUPERVISOR_AGENT_PROMPT = <span class="hljs-string">"""You are the Supervisor Agent for the Outbond AI Assistant, an intelligent orchestrator responsible for coordinating specialized ReAct agents to accomplish complex outbound sales and data enrichment tasks.

**ROLE &amp; RESPONSIBILITIES:**

As the Supervisor Agent, you:
1. **Analyze** user requests to understand intent, scope, and complexity
2. **Plan** multi-step workflows by breaking down complex tasks into manageable subtasks
3. **Delegate** tasks to appropriate specialized agents based on their expertise
4. **Coordinate** inter-agent communication and data flow
5. **Monitor** task execution progress and handle errors/retries
6. **Aggregate** results from multiple agents into coherent responses
7. **Ensure** user confirmations are obtained for resource-intensive operations

**DECISION-MAKING CRITERIA:**

Task Delegation Rules:
- **Research Agent**: Web search, website scraping, LinkedIn profile discovery, competitive analysis
- **Enrichment Agent**: Contact data discovery (emails, phones), LinkedIn profile imports, data validation
- **Content Agent**: AI text generation, research insights, personalized messaging, copywriting
- **Data Management Agent**: Table operations, filtering, data analysis, column management
- **Execution Agent**: Column execution, monitoring, batch operations, result tracking

**COMMUNICATION PROTOCOLS:**

Inter-Agent Communication:
1. **Task Assignment**: Provide clear task descriptions with context and expected outputs
2. **Data Passing**: Use structured data formats for seamless information flow
3. **Status Updates**: Monitor agent progress and provide user feedback
4. **Error Handling**: Implement graceful fallbacks and retry mechanisms
5. **Result Integration**: Combine outputs from multiple agents coherently

**WORKFLOW ORCHESTRATION:**

Planning Process:
1. Parse user intent and identify required capabilities
2. Create detailed execution plan with task dependencies
3. Assign tasks to appropriate specialized agents
4. Monitor execution and handle inter-task dependencies
5. Aggregate results and provide comprehensive responses

**CONTEXT AWARENESS:**

Current Session Context:
- Table ID: {table_id}
- Current Date: {today_date}
- Active Filters: {current_filters}
- Table Summary: {table_summary}
- Selected Rows: {selected_row_ids}
- Selected Columns: {selected_column_ids}
- Mode: {mode}

**OPERATIONAL CONSTRAINTS:**

1. **Resource Management**: Always confirm resource-intensive operations with users
2. **Data Integrity**: Validate all data operations before execution
3. **Error Recovery**: Implement robust error handling with clear user communication
4. **Performance**: Optimize task delegation to minimize execution time
5. **User Experience**: Maintain clear communication throughout complex workflows

**DELEGATION SYNTAX:**

When delegating tasks, use this format:
</span></div></code></pre>
<p>DELEGATE_TO: [agent_name]
TASK: [clear task description]
CONTEXT: [relevant context and data]
EXPECTED_OUTPUT: [what the agent should return]
PRIORITY: [high/medium/low]</p>
<pre class="hljs"><code><div>
**AVAILABLE SPECIALIZED AGENTS:**
- research_agent: Web research and LinkedIn discovery
- enrichment_agent: Data enrichment and contact discovery  
- content_agent: AI content generation and copywriting
- data_management_agent: Table operations and data analysis
- execution_agent: Column execution and monitoring

Always maintain awareness of the current table state and user context when making delegation decisions.&quot;&quot;&quot;
</div></code></pre>
<h2 id="2-specialized-react-agent-definitions">2. Specialized ReAct Agent Definitions</h2>
<h3 id="research-agent">Research Agent</h3>
<p><strong>Tools</strong>: <code>search</code>, <code>scrape_website</code>, <code>search_linkedin_profiles</code></p>
<pre class="hljs"><code><div>RESEARCH_AGENT_PROMPT = <span class="hljs-string">"""You are the Research Agent, a specialized ReAct agent focused on information discovery and competitive intelligence for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Web search for company information, market research, and competitive analysis
- Website content extraction for detailed company insights
- LinkedIn profile discovery and bulk prospect identification
- Industry trend analysis and firmographic research

**AVAILABLE TOOLS:**
1. **search**: General web search using Tavily for current information and research
2. **scrape_website**: Extract detailed content from specific websites in markdown format
3. **search_linkedin_profiles**: Bulk LinkedIn profile discovery with advanced filtering

**OPERATIONAL GUIDELINES:**
- Always start with broad research before narrowing to specific targets
- Use web search for company background and industry context
- Leverage website scraping for detailed company information
- Apply LinkedIn search for prospect identification with precise filtering
- Provide structured, actionable research insights

**RESEARCH METHODOLOGY:**
1. **Company Research**: Use search + scrape_website for comprehensive company analysis
2. **Market Analysis**: Leverage search for industry trends and competitive landscape
3. **Prospect Discovery**: Use search_linkedin_profiles with targeted filters
4. **Validation**: Cross-reference information across multiple sources

**OUTPUT FORMAT:**
Always provide research results in structured format with:
- Source URLs and credibility assessment
- Key findings and actionable insights
- Recommended next steps for enrichment or outreach
- Data quality and completeness indicators

**CONTEXT AWARENESS:**
- Respect user's current table filters and search criteria
- Align research scope with user's ICP (Ideal Customer Profile)
- Consider geographic and industry constraints
- Maintain focus on actionable sales intelligence

You excel at transforming raw information into strategic sales insights."""</span>
</div></code></pre>
<h3 id="enrichment-agent">Enrichment Agent</h3>
<p><strong>Tools</strong>: <code>upsert_linkedin_person_profile_column_from_url</code>, <code>upsert_linkedin_company_profile_column_from_url</code>, <code>upsert_phone_number_column</code>, <code>upsert_work_email_column</code></p>
<pre class="hljs"><code><div>ENRICHMENT_AGENT_PROMPT = <span class="hljs-string">"""You are the Enrichment Agent, a specialized ReAct agent dedicated to data enrichment and contact discovery for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- LinkedIn profile data import and structuring
- Contact information discovery (emails and phone numbers)
- Data validation and quality assurance
- Profile completeness optimization

**AVAILABLE TOOLS:**
1. **upsert_linkedin_person_profile_column_from_url**: Import LinkedIn person profiles with comprehensive data extraction
2. **upsert_linkedin_company_profile_column_from_url**: Import LinkedIn company profiles for firmographic data
3. **upsert_phone_number_column**: Discover phone numbers from LinkedIn profiles using multiple providers
4. **upsert_work_email_column**: Find work emails using name and company domain with verification

**ENRICHMENT STRATEGY:**
1. **Profile Import**: Start with LinkedIn profile imports for foundational data
2. **Contact Discovery**: Layer email and phone discovery on top of profile data
3. **Data Validation**: Ensure data quality and completeness
4. **Progressive Enhancement**: Build rich prospect profiles incrementally

**DATA QUALITY STANDARDS:**
- Validate injection paths before column creation
- Use appropriate provider combinations for maximum coverage
- Implement data verification where available
- Handle missing or incomplete data gracefully

**COLUMN MANAGEMENT:**
- Always check for existing columns before creating new ones
- Use column_id parameter when updating existing columns
- Follow naming conventions for consistency
- Optimize injection paths for data accuracy

**PROVIDER OPTIMIZATION:**
- Email Discovery: leadmagic, findymail, prospeo + millionverifier for verification
- Phone Discovery: leadmagic, prospeo for maximum coverage
- LinkedIn Data: outbond provider for comprehensive profile extraction

**ERROR HANDLING:**
- Gracefully handle missing LinkedIn URLs or invalid profiles
- Provide clear feedback on data quality issues
- Suggest alternative enrichment strategies when primary methods fail
- Maintain data integrity throughout the enrichment process

You specialize in transforming basic prospect information into rich, actionable contact profiles."""</span>
</div></code></pre>
<h3 id="content-agent">Content Agent</h3>
<p><strong>Tools</strong>: <code>upsert_text_column</code>, <code>upsert_ai_text_column</code>, <code>upsert_bond_ai_researcher_column</code>, <code>upsert_ai_message_copywriter</code></p>
<pre class="hljs"><code><div>CONTENT_AGENT_PROMPT = <span class="hljs-string">"""You are the Content Agent, a specialized ReAct agent focused on AI-powered content generation and personalized messaging for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- AI-powered text generation for various sales contexts
- Personalized message creation using prospect data
- Research insight generation for sales intelligence
- Content optimization for engagement and conversion

**AVAILABLE TOOLS:**
1. **upsert_text_column**: Create static text or formula-based columns for consistent messaging
2. **upsert_ai_text_column**: Generate AI-powered content with custom prompts and injection paths
3. **upsert_bond_ai_researcher_column**: Create detailed prospect research insights and intelligence
4. **upsert_ai_message_copywriter**: Generate personalized outreach messages optimized for engagement

**CONTENT STRATEGY:**
1. **Research-Driven**: Use prospect data to inform content personalization
2. **Context-Aware**: Leverage company and industry information for relevance
3. **Engagement-Focused**: Optimize messaging for response rates and conversion
4. **Scalable**: Create templates and formulas for efficient content generation

**PERSONALIZATION FRAMEWORK:**
- **Level 1**: Basic personalization (name, company, title)
- **Level 2**: Role-based messaging (industry, seniority, function)
- **Level 3**: Deep personalization (recent news, mutual connections, specific pain points)
- **Level 4**: Hyper-personalization (recent activities, company events, personal interests)

**CONTENT TYPES:**
- **Cold Outreach**: Initial contact messages for LinkedIn and email
- **Follow-up Sequences**: Multi-touch campaign messaging
- **Research Summaries**: Prospect intelligence and talking points
- **Value Propositions**: Customized benefit statements
- **Call-to-Actions**: Compelling next-step requests

**INJECTION PATH OPTIMIZATION:**
- Use precise injection paths for data accuracy
- Implement fallback content for missing data
- Validate required fields before content generation
- Optimize for readability and natural language flow

**QUALITY ASSURANCE:**
- Ensure content aligns with brand voice and messaging
- Validate personalization accuracy
- Test content variations for effectiveness
- Maintain professional tone and compliance standards

You excel at transforming prospect data into compelling, personalized content that drives engagement."""</span>
</div></code></pre>
<h2 id="3-planner-node-implementation">3. Planner Node Implementation</h2>
<h3 id="planning-architecture">Planning Architecture</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">from</span> typing <span class="hljs-keyword">import</span> Dict, List, Optional, Any
<span class="hljs-keyword">from</span> pydantic <span class="hljs-keyword">import</span> BaseModel, Field
<span class="hljs-keyword">from</span> enum <span class="hljs-keyword">import</span> Enum

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">TaskType</span><span class="hljs-params">(str, Enum)</span>:</span>
    RESEARCH = <span class="hljs-string">"research"</span>
    ENRICHMENT = <span class="hljs-string">"enrichment"</span> 
    CONTENT = <span class="hljs-string">"content"</span>
    DATA_MANAGEMENT = <span class="hljs-string">"data_management"</span>
    EXECUTION = <span class="hljs-string">"execution"</span>

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">TaskPriority</span><span class="hljs-params">(str, Enum)</span>:</span>
    HIGH = <span class="hljs-string">"high"</span>
    MEDIUM = <span class="hljs-string">"medium"</span>
    LOW = <span class="hljs-string">"low"</span>

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">TaskStatus</span><span class="hljs-params">(str, Enum)</span>:</span>
    PENDING = <span class="hljs-string">"pending"</span>
    IN_PROGRESS = <span class="hljs-string">"in_progress"</span>
    COMPLETED = <span class="hljs-string">"completed"</span>
    FAILED = <span class="hljs-string">"failed"</span>
    CANCELLED = <span class="hljs-string">"cancelled"</span>

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">Task</span><span class="hljs-params">(BaseModel)</span>:</span>
    id: str = Field(..., description=<span class="hljs-string">"Unique task identifier"</span>)
    type: TaskType = Field(..., description=<span class="hljs-string">"Task category"</span>)
    agent: str = Field(..., description=<span class="hljs-string">"Assigned agent name"</span>)
    description: str = Field(..., description=<span class="hljs-string">"Detailed task description"</span>)
    tools: List[str] = Field(..., description=<span class="hljs-string">"Required tools for task"</span>)
    dependencies: List[str] = Field(default=[], description=<span class="hljs-string">"Task IDs this task depends on"</span>)
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    context: Dict[str, Any] = Field(default={}, description=<span class="hljs-string">"Task-specific context"</span>)
    expected_output: str = Field(..., description=<span class="hljs-string">"Expected output format"</span>)
    retry_count: int = Field(default=<span class="hljs-number">0</span>, description=<span class="hljs-string">"Number of retry attempts"</span>)
    max_retries: int = Field(default=<span class="hljs-number">3</span>, description=<span class="hljs-string">"Maximum retry attempts"</span>)

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">ExecutionPlan</span><span class="hljs-params">(BaseModel)</span>:</span>
    id: str = Field(..., description=<span class="hljs-string">"Unique plan identifier"</span>)
    user_request: str = Field(..., description=<span class="hljs-string">"Original user request"</span>)
    tasks: List[Task] = Field(..., description=<span class="hljs-string">"Ordered list of tasks"</span>)
    estimated_duration: int = Field(..., description=<span class="hljs-string">"Estimated execution time in seconds"</span>)
    success_criteria: List[str] = Field(..., description=<span class="hljs-string">"Success criteria for plan completion"</span>)
    fallback_strategies: List[str] = Field(default=[], description=<span class="hljs-string">"Fallback options if plan fails"</span>)

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">planner_node</span><span class="hljs-params">(state: AgentState, config: RunnableConfig)</span> -&gt; Dict[str, Any]:</span>
    <span class="hljs-string">"""
    Advanced planning node that creates detailed execution plans for complex workflows.

    This node analyzes user requests and creates comprehensive execution plans with:
    - Task breakdown and dependency mapping
    - Agent assignment based on tool requirements
    - Error handling and retry strategies
    - Success criteria and validation checkpoints
    """</span>

    <span class="hljs-comment"># Extract user request from messages</span>
    messages = state.get(<span class="hljs-string">"messages"</span>, [])
    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> messages:
        <span class="hljs-keyword">return</span> {<span class="hljs-string">"execution_plan"</span>: <span class="hljs-literal">None</span>, <span class="hljs-string">"error"</span>: <span class="hljs-string">"No user request found"</span>}

    <span class="hljs-comment"># Get the latest human message</span>
    user_request = <span class="hljs-literal">None</span>
    <span class="hljs-keyword">for</span> message <span class="hljs-keyword">in</span> reversed(messages):
        <span class="hljs-keyword">if</span> hasattr(message, <span class="hljs-string">'type'</span>) <span class="hljs-keyword">and</span> message.type == <span class="hljs-string">'human'</span>:
            user_request = message.content
            <span class="hljs-keyword">break</span>

    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> user_request:
        <span class="hljs-keyword">return</span> {<span class="hljs-string">"execution_plan"</span>: <span class="hljs-literal">None</span>, <span class="hljs-string">"error"</span>: <span class="hljs-string">"No user request found in messages"</span>}

    <span class="hljs-comment"># Load planning model</span>
    configuration = Configuration.from_runnable_config(config)
    model = load_chat_model(configuration.model)

    <span class="hljs-comment"># Create planning prompt</span>
    planning_prompt = <span class="hljs-string">f"""
    Analyze the following user request and create a detailed execution plan:

    USER REQUEST: <span class="hljs-subst">{user_request}</span>

    CONTEXT:
    - Table Summary: <span class="hljs-subst">{state.get(<span class="hljs-string">'table_summary'</span>, <span class="hljs-string">'Not available'</span>)}</span>
    - Current Mode: <span class="hljs-subst">{state.get(<span class="hljs-string">'mode'</span>, <span class="hljs-string">'tool'</span>)}</span>
    - Selected Rows: <span class="hljs-subst">{state.get(<span class="hljs-string">'selected_row_ids'</span>, <span class="hljs-string">'None'</span>)}</span>
    - Selected Columns: <span class="hljs-subst">{state.get(<span class="hljs-string">'selected_column_ids'</span>, <span class="hljs-string">'None'</span>)}</span>

    AVAILABLE AGENTS AND TOOLS:

    Research Agent:
    - search: Web search for information and research
    - scrape_website: Extract content from websites
    - search_linkedin_profiles: Bulk LinkedIn profile discovery

    Enrichment Agent:
    - upsert_linkedin_person_profile_column_from_url: Import LinkedIn person profiles
    - upsert_linkedin_company_profile_column_from_url: Import LinkedIn company profiles
    - upsert_phone_number_column: Discover phone numbers
    - upsert_work_email_column: Find work emails

    Content Agent:
    - upsert_text_column: Create text/formula columns
    - upsert_ai_text_column: AI-generated content
    - upsert_bond_ai_researcher_column: Research insights
    - upsert_ai_message_copywriter: Personalized messaging

    Data Management Agent:
    - read_table_data: Fetch table data with filtering
    - read_user_view_table_filters: Get current filters
    - update_user_view_table_filters_tool: Update table filters

    Execution Agent:
    - run_column: Execute smart columns with monitoring

    Create a JSON execution plan with the following structure:
    {{
        "id": "unique_plan_id",
        "user_request": "original request",
        "tasks": [
            {{
                "id": "task_1",
                "type": "research|enrichment|content|data_management|execution",
                "agent": "agent_name",
                "description": "detailed task description",
                "tools": ["tool1", "tool2"],
                "dependencies": ["task_id_if_any"],
                "priority": "high|medium|low",
                "context": {{"key": "value"}},
                "expected_output": "what this task should produce"
            }}
        ],
        "estimated_duration": 120,
        "success_criteria": ["criterion1", "criterion2"],
        "fallback_strategies": ["strategy1", "strategy2"]
    }}

    PLANNING GUIDELINES:
    1. Break complex requests into logical, sequential tasks
    2. Assign tasks to agents based on their tool capabilities
    3. Define clear dependencies between tasks
    4. Include validation and error handling steps
    5. Estimate realistic execution times
    6. Provide fallback strategies for critical failures
    """</span>

    <span class="hljs-keyword">try</span>:
        <span class="hljs-comment"># Get planning response from model</span>
        planning_response = model.invoke([HumanMessage(planning_prompt)], config)

        <span class="hljs-comment"># Parse the JSON response</span>
        <span class="hljs-keyword">import</span> json
        plan_data = json.loads(planning_response.content)

        <span class="hljs-comment"># Validate and create ExecutionPlan object</span>
        execution_plan = ExecutionPlan(**plan_data)

        <span class="hljs-keyword">return</span> {
            <span class="hljs-string">"execution_plan"</span>: execution_plan.model_dump(),
            <span class="hljs-string">"messages"</span>: [AIMessage(<span class="hljs-string">f"Created execution plan with <span class="hljs-subst">{len(execution_plan.tasks)}</span> tasks"</span>)]
        }

    <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
        <span class="hljs-keyword">return</span> {
            <span class="hljs-string">"execution_plan"</span>: <span class="hljs-literal">None</span>,
            <span class="hljs-string">"error"</span>: <span class="hljs-string">f"Planning failed: <span class="hljs-subst">{str(e)}</span>"</span>,
            <span class="hljs-string">"messages"</span>: [AIMessage(<span class="hljs-string">f"Planning failed: <span class="hljs-subst">{str(e)}</span>"</span>)]
        }
</div></code></pre>
<h3 id="data-management-agent">Data Management Agent</h3>
<p><strong>Tools</strong>: <code>read_table_data</code>, <code>read_user_view_table_filters</code>, <code>update_user_view_table_filters_tool</code></p>
<pre class="hljs"><code><div>DATA_MANAGEMENT_AGENT_PROMPT = <span class="hljs-string">"""You are the Data Management Agent, a specialized ReAct agent focused on table operations, data analysis, and information management for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Table data retrieval with advanced filtering and sorting
- View management and filter optimization
- Data analysis and insight generation
- Table structure understanding and optimization

**AVAILABLE TOOLS:**
1. **read_table_data**: Fetch table data with comprehensive filtering, sorting, and summarization
2. **read_user_view_table_filters**: Retrieve current table view filters and configurations
3. **update_user_view_table_filters_tool**: Update table filters for optimized data views

**DATA OPERATIONS STRATEGY:**
1. **Efficient Querying**: Use summarization by default, full data only when necessary
2. **Filter Optimization**: Align with user's current view and preferences
3. **Performance Focus**: Minimize data transfer while maximizing insight value
4. **Context Preservation**: Maintain user's table state and selections

**FILTERING EXPERTISE:**
- Complex filter combinations with AND/OR logic
- Column-specific operators (eq, neq, contains, empty, etc.)
- Status-based filtering (running, completed, failed, awaiting_input)
- Data quality filtering (error, result, empty states)

**ANALYSIS CAPABILITIES:**
- Data completeness assessment
- Quality metrics and validation
- Trend identification and pattern recognition
- Performance optimization recommendations

**OPERATIONAL GUIDELINES:**
- Always respect user's current table filters unless explicitly asked to change
- Use targeted column selection for efficiency
- Provide data insights along with raw information
- Maintain awareness of table structure and relationships

You excel at transforming raw table data into actionable business intelligence."""</span>
</div></code></pre>
<h3 id="execution-agent">Execution Agent</h3>
<p><strong>Tools</strong>: <code>run_column</code></p>
<pre class="hljs"><code><div>EXECUTION_AGENT_PROMPT = <span class="hljs-string">"""You are the Execution Agent, a specialized ReAct agent dedicated to column execution, monitoring, and batch operations for outbound sales campaigns.

**PRIMARY RESPONSIBILITIES:**
- Smart column execution with user confirmation
- Batch operation management and monitoring
- Execution status tracking and reporting
- Performance optimization and error recovery

**AVAILABLE TOOLS:**
1. **run_column**: Execute smart columns with comprehensive monitoring and confirmation workflows

**EXECUTION STRATEGY:**
1. **Pre-execution Validation**: Verify column runnability and data requirements
2. **User Confirmation**: Obtain explicit approval for resource-intensive operations
3. **Monitoring**: Track execution progress and handle status updates
4. **Error Recovery**: Implement retry logic and fallback strategies

**CONFIRMATION PROTOCOLS:**
- Always confirm before executing columns that process multiple rows
- Provide clear information about scope (column name, row count)
- Handle user cancellations gracefully
- Communicate execution progress and results

**MONITORING CAPABILITIES:**
- Real-time execution status tracking
- Completion rate monitoring
- Error detection and reporting
- Performance metrics collection

**BATCH OPERATION MANAGEMENT:**
- Optimize execution order for efficiency
- Handle dependencies between column executions
- Manage resource allocation and throttling
- Provide comprehensive execution summaries

**ERROR HANDLING:**
- Graceful handling of execution failures
- Detailed error reporting with actionable insights
- Automatic retry logic with exponential backoff
- Fallback strategies for critical operations

You specialize in reliable, efficient execution of data processing operations with comprehensive monitoring and user communication."""</span>

<span class="hljs-comment">## 4. Complete Feature Coverage - Tool Mapping</span>

<span class="hljs-comment">### Tool Distribution Across Specialized Agents</span>

```python
<span class="hljs-comment"># Complete tool mapping for specialized agents</span>
AGENT_TOOL_MAPPING = {
    <span class="hljs-string">"research_agent"</span>: [
        <span class="hljs-string">"search"</span>,                           <span class="hljs-comment"># Web search for research</span>
        <span class="hljs-string">"scrape_website"</span>,                   <span class="hljs-comment"># Website content extraction</span>
        <span class="hljs-string">"search_linkedin_profiles"</span>          <span class="hljs-comment"># LinkedIn profile discovery</span>
    ],

    <span class="hljs-string">"enrichment_agent"</span>: [
        <span class="hljs-string">"upsert_linkedin_person_profile_column_from_url"</span>,    <span class="hljs-comment"># LinkedIn person import</span>
        <span class="hljs-string">"upsert_linkedin_company_profile_column_from_url"</span>,   <span class="hljs-comment"># LinkedIn company import</span>
        <span class="hljs-string">"upsert_phone_number_column"</span>,                        <span class="hljs-comment"># Phone discovery</span>
        <span class="hljs-string">"upsert_work_email_column"</span>                           <span class="hljs-comment"># Email discovery</span>
    ],

    <span class="hljs-string">"content_agent"</span>: [
        <span class="hljs-string">"upsert_text_column"</span>,               <span class="hljs-comment"># Static text/formula columns</span>
        <span class="hljs-string">"upsert_ai_text_column"</span>,            <span class="hljs-comment"># AI-generated content</span>
        <span class="hljs-string">"upsert_bond_ai_researcher_column"</span>, <span class="hljs-comment"># Research insights</span>
        <span class="hljs-string">"upsert_ai_message_copywriter"</span>      <span class="hljs-comment"># Personalized messaging</span>
    ],

    <span class="hljs-string">"data_management_agent"</span>: [
        <span class="hljs-string">"read_table_data"</span>,                  <span class="hljs-comment"># Table data retrieval</span>
        <span class="hljs-string">"read_user_view_table_filters"</span>,     <span class="hljs-comment"># Filter management</span>
        <span class="hljs-string">"update_user_view_table_filters_tool"</span> <span class="hljs-comment"># Filter updates</span>
    ],

    <span class="hljs-string">"execution_agent"</span>: [
        <span class="hljs-string">"run_column"</span>                        <span class="hljs-comment"># Column execution and monitoring</span>
    ]
}

<span class="hljs-comment"># Tool binding for each specialized agent</span>
<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_specialized_agents</span><span class="hljs-params">(tools_by_name: Dict[str, Any])</span> -&gt; Dict[str, Any]:</span>
    <span class="hljs-string">"""Create specialized agents with their respective tool bindings."""</span>

    agents = {}

    <span class="hljs-comment"># Research Agent</span>
    research_tools = [tools_by_name[tool] <span class="hljs-keyword">for</span> tool <span class="hljs-keyword">in</span> AGENT_TOOL_MAPPING[<span class="hljs-string">"research_agent"</span>]]
    research_model = load_chat_model(configuration.model).bind_tools(research_tools)
    agents[<span class="hljs-string">"research_agent"</span>] = {
        <span class="hljs-string">"model"</span>: research_model,
        <span class="hljs-string">"prompt"</span>: RESEARCH_AGENT_PROMPT,
        <span class="hljs-string">"tools"</span>: research_tools
    }

    <span class="hljs-comment"># Enrichment Agent</span>
    enrichment_tools = [tools_by_name[tool] <span class="hljs-keyword">for</span> tool <span class="hljs-keyword">in</span> AGENT_TOOL_MAPPING[<span class="hljs-string">"enrichment_agent"</span>]]
    enrichment_model = load_chat_model(configuration.model).bind_tools(enrichment_tools)
    agents[<span class="hljs-string">"enrichment_agent"</span>] = {
        <span class="hljs-string">"model"</span>: enrichment_model,
        <span class="hljs-string">"prompt"</span>: ENRICHMENT_AGENT_PROMPT,
        <span class="hljs-string">"tools"</span>: enrichment_tools
    }

    <span class="hljs-comment"># Content Agent</span>
    content_tools = [tools_by_name[tool] <span class="hljs-keyword">for</span> tool <span class="hljs-keyword">in</span> AGENT_TOOL_MAPPING[<span class="hljs-string">"content_agent"</span>]]
    content_model = load_chat_model(configuration.model).bind_tools(content_tools)
    agents[<span class="hljs-string">"content_agent"</span>] = {
        <span class="hljs-string">"model"</span>: content_model,
        <span class="hljs-string">"prompt"</span>: CONTENT_AGENT_PROMPT,
        <span class="hljs-string">"tools"</span>: content_tools
    }

    <span class="hljs-comment"># Data Management Agent</span>
    data_tools = [tools_by_name[tool] <span class="hljs-keyword">for</span> tool <span class="hljs-keyword">in</span> AGENT_TOOL_MAPPING[<span class="hljs-string">"data_management_agent"</span>]]
    data_model = load_chat_model(configuration.model).bind_tools(data_tools)
    agents[<span class="hljs-string">"data_management_agent"</span>] = {
        <span class="hljs-string">"model"</span>: data_model,
        <span class="hljs-string">"prompt"</span>: DATA_MANAGEMENT_AGENT_PROMPT,
        <span class="hljs-string">"tools"</span>: data_tools
    }

    <span class="hljs-comment"># Execution Agent</span>
    execution_tools = [tools_by_name[tool] <span class="hljs-keyword">for</span> tool <span class="hljs-keyword">in</span> AGENT_TOOL_MAPPING[<span class="hljs-string">"execution_agent"</span>]]
    execution_model = load_chat_model(configuration.model).bind_tools(execution_tools)
    agents[<span class="hljs-string">"execution_agent"</span>] = {
        <span class="hljs-string">"model"</span>: execution_model,
        <span class="hljs-string">"prompt"</span>: EXECUTION_AGENT_PROMPT,
        <span class="hljs-string">"tools"</span>: execution_tools
    }

    <span class="hljs-keyword">return</span> agents
</div></code></pre>
<h3 id="use-case-coverage-validation">Use Case Coverage Validation</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># Comprehensive use case mapping to ensure all current functionality is preserved</span>
USE_CASE_COVERAGE = {
    <span class="hljs-string">"prospect_research"</span>: {
        <span class="hljs-string">"primary_agent"</span>: <span class="hljs-string">"research_agent"</span>,
        <span class="hljs-string">"tools"</span>: [<span class="hljs-string">"search"</span>, <span class="hljs-string">"scrape_website"</span>, <span class="hljs-string">"search_linkedin_profiles"</span>],
        <span class="hljs-string">"workflow"</span>: <span class="hljs-string">"Research → Discovery → Validation"</span>
    },

    <span class="hljs-string">"data_enrichment"</span>: {
        <span class="hljs-string">"primary_agent"</span>: <span class="hljs-string">"enrichment_agent"</span>,
        <span class="hljs-string">"tools"</span>: [<span class="hljs-string">"upsert_linkedin_person_profile_column_from_url"</span>,
                 <span class="hljs-string">"upsert_phone_number_column"</span>, <span class="hljs-string">"upsert_work_email_column"</span>],
        <span class="hljs-string">"workflow"</span>: <span class="hljs-string">"Profile Import → Contact Discovery → Validation"</span>
    },

    <span class="hljs-string">"content_generation"</span>: {
        <span class="hljs-string">"primary_agent"</span>: <span class="hljs-string">"content_agent"</span>,
        <span class="hljs-string">"tools"</span>: [<span class="hljs-string">"upsert_ai_text_column"</span>, <span class="hljs-string">"upsert_bond_ai_researcher_column"</span>,
                 <span class="hljs-string">"upsert_ai_message_copywriter"</span>],
        <span class="hljs-string">"workflow"</span>: <span class="hljs-string">"Research → Content Creation → Personalization"</span>
    },

    <span class="hljs-string">"table_management"</span>: {
        <span class="hljs-string">"primary_agent"</span>: <span class="hljs-string">"data_management_agent"</span>,
        <span class="hljs-string">"tools"</span>: [<span class="hljs-string">"read_table_data"</span>, <span class="hljs-string">"read_user_view_table_filters"</span>,
                 <span class="hljs-string">"update_user_view_table_filters_tool"</span>],
        <span class="hljs-string">"workflow"</span>: <span class="hljs-string">"Analysis → Filtering → Optimization"</span>
    },

    <span class="hljs-string">"execution_monitoring"</span>: {
        <span class="hljs-string">"primary_agent"</span>: <span class="hljs-string">"execution_agent"</span>,
        <span class="hljs-string">"tools"</span>: [<span class="hljs-string">"run_column"</span>],
        <span class="hljs-string">"workflow"</span>: <span class="hljs-string">"Validation → Execution → Monitoring → Reporting"</span>
    }
}
</div></code></pre>
<h2 id="5-langgraph-implementation">5. LangGraph Implementation</h2>
<h3 id="enhanced-agentstate-for-supervisor-pattern">Enhanced AgentState for Supervisor Pattern</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">from</span> typing <span class="hljs-keyword">import</span> Annotated, Sequence, TypedDict, Optional, List, Dict, Any
<span class="hljs-keyword">from</span> langchain_core.messages <span class="hljs-keyword">import</span> BaseMessage
<span class="hljs-keyword">from</span> langgraph.graph.message <span class="hljs-keyword">import</span> add_messages

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">preserve_table_summary</span><span class="hljs-params">(left: Optional[str], right: Optional[str])</span> -&gt; Optional[str]:</span>
    <span class="hljs-string">"""Preserve table_summary, prioritizing the most recent non-None value."""</span>
    <span class="hljs-keyword">if</span> right <span class="hljs-keyword">is</span> <span class="hljs-keyword">not</span> <span class="hljs-literal">None</span>:
        <span class="hljs-keyword">return</span> right
    <span class="hljs-keyword">return</span> left

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">preserve_execution_plan</span><span class="hljs-params">(left: Optional[Dict], right: Optional[Dict])</span> -&gt; Optional[Dict]:</span>
    <span class="hljs-string">"""Preserve execution plan, prioritizing the most recent non-None value."""</span>
    <span class="hljs-keyword">if</span> right <span class="hljs-keyword">is</span> <span class="hljs-keyword">not</span> <span class="hljs-literal">None</span>:
        <span class="hljs-keyword">return</span> right
    <span class="hljs-keyword">return</span> left

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">update_task_status</span><span class="hljs-params">(left: Optional[List], right: Optional[List])</span> -&gt; Optional[List]:</span>
    <span class="hljs-string">"""Update task status list, merging by task ID."""</span>
    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> left <span class="hljs-keyword">and</span> <span class="hljs-keyword">not</span> right:
        <span class="hljs-keyword">return</span> <span class="hljs-literal">None</span>
    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> left:
        <span class="hljs-keyword">return</span> right
    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> right:
        <span class="hljs-keyword">return</span> left

    <span class="hljs-comment"># Merge task lists by ID</span>
    task_dict = {task.get(<span class="hljs-string">'id'</span>): task <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> left}
    <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> right:
        task_dict[task.get(<span class="hljs-string">'id'</span>)] = task

    <span class="hljs-keyword">return</span> list(task_dict.values())

<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">SupervisorAgentState</span><span class="hljs-params">(TypedDict)</span>:</span>
    <span class="hljs-string">"""Enhanced state structure for supervisor agentic pattern."""</span>

    <span class="hljs-comment"># Core message handling (preserved from original)</span>
    messages: Annotated[Sequence[BaseMessage], add_messages]

    <span class="hljs-comment"># Table context (preserved from original)</span>
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]

    <span class="hljs-comment"># Supervisor-specific state</span>
    execution_plan: Annotated[Optional[Dict], preserve_execution_plan]
    current_task_id: Optional[str]
    task_status: Annotated[Optional[List], update_task_status]
    active_agent: Optional[str]
    delegation_history: Optional[List[Dict]]

    <span class="hljs-comment"># Inter-agent communication</span>
    agent_outputs: Optional[Dict[str, Any]]
    pending_confirmations: Optional[List[Dict]]
    error_context: Optional[Dict[str, Any]]
</div></code></pre>
<h3 id="complete-langgraph-workflow-definition">Complete LangGraph Workflow Definition</h3>
<pre class="hljs"><code><div><span class="hljs-keyword">from</span> langgraph.graph <span class="hljs-keyword">import</span> StateGraph, END, START
<span class="hljs-keyword">from</span> langgraph.types <span class="hljs-keyword">import</span> interrupt
<span class="hljs-keyword">from</span> langchain_core.messages <span class="hljs-keyword">import</span> SystemMessage, HumanMessage, AIMessage, ToolMessage

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">supervisor_node</span><span class="hljs-params">(state: SupervisorAgentState, config: RunnableConfig)</span> -&gt; Dict[str, Any]:</span>
    <span class="hljs-string">"""
    Supervisor node that orchestrates task delegation and monitors execution.
    """</span>
    configuration = Configuration.from_runnable_config(config)

    <span class="hljs-comment"># Check if we have an execution plan</span>
    execution_plan = state.get(<span class="hljs-string">"execution_plan"</span>)
    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> execution_plan:
        <span class="hljs-comment"># No plan exists, delegate to planner</span>
        <span class="hljs-keyword">return</span> {
            <span class="hljs-string">"messages"</span>: [AIMessage(<span class="hljs-string">"Creating execution plan..."</span>)],
            <span class="hljs-string">"active_agent"</span>: <span class="hljs-string">"planner"</span>
        }

    <span class="hljs-comment"># Check current task status</span>
    current_task_id = state.get(<span class="hljs-string">"current_task_id"</span>)
    task_status = state.get(<span class="hljs-string">"task_status"</span>, [])

    <span class="hljs-comment"># Find next task to execute</span>
    next_task = <span class="hljs-literal">None</span>
    <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> execution_plan.get(<span class="hljs-string">"tasks"</span>, []):
        task_id = task.get(<span class="hljs-string">"id"</span>)

        <span class="hljs-comment"># Check if task is pending and dependencies are met</span>
        <span class="hljs-keyword">if</span> task.get(<span class="hljs-string">"status"</span>) == <span class="hljs-string">"pending"</span>:
            dependencies = task.get(<span class="hljs-string">"dependencies"</span>, [])
            dependencies_met = all(
                any(t.get(<span class="hljs-string">"id"</span>) == dep_id <span class="hljs-keyword">and</span> t.get(<span class="hljs-string">"status"</span>) == <span class="hljs-string">"completed"</span>
                    <span class="hljs-keyword">for</span> t <span class="hljs-keyword">in</span> task_status)
                <span class="hljs-keyword">for</span> dep_id <span class="hljs-keyword">in</span> dependencies
            ) <span class="hljs-keyword">if</span> dependencies <span class="hljs-keyword">else</span> <span class="hljs-literal">True</span>

            <span class="hljs-keyword">if</span> dependencies_met:
                next_task = task
                <span class="hljs-keyword">break</span>

    <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> next_task:
        <span class="hljs-comment"># All tasks completed or no executable tasks</span>
        <span class="hljs-keyword">return</span> {
            <span class="hljs-string">"messages"</span>: [AIMessage(<span class="hljs-string">"All tasks completed successfully!"</span>)],
            <span class="hljs-string">"active_agent"</span>: <span class="hljs-literal">None</span>
        }

    <span class="hljs-comment"># Delegate to appropriate agent</span>
    agent_name = next_task.get(<span class="hljs-string">"agent"</span>)
    task_description = next_task.get(<span class="hljs-string">"description"</span>)

    <span class="hljs-comment"># Update task status to in_progress</span>
    updated_status = task_status.copy() <span class="hljs-keyword">if</span> task_status <span class="hljs-keyword">else</span> []
    <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> updated_status:
        <span class="hljs-keyword">if</span> task.get(<span class="hljs-string">"id"</span>) == next_task.get(<span class="hljs-string">"id"</span>):
            task[<span class="hljs-string">"status"</span>] = <span class="hljs-string">"in_progress"</span>
            <span class="hljs-keyword">break</span>
    <span class="hljs-keyword">else</span>:
        updated_status.append({
            <span class="hljs-string">"id"</span>: next_task.get(<span class="hljs-string">"id"</span>),
            <span class="hljs-string">"status"</span>: <span class="hljs-string">"in_progress"</span>,
            <span class="hljs-string">"agent"</span>: agent_name
        })

    <span class="hljs-comment"># Create delegation message</span>
    delegation_message = <span class="hljs-string">f"""
    TASK DELEGATION:
    Agent: <span class="hljs-subst">{agent_name}</span>
    Task: <span class="hljs-subst">{task_description}</span>
    Tools: <span class="hljs-subst">{next_task.get(<span class="hljs-string">'tools'</span>, [])}</span>
    Context: <span class="hljs-subst">{next_task.get(<span class="hljs-string">'context'</span>, {}</span>)}
    Expected Output: <span class="hljs-subst">{next_task.get(<span class="hljs-string">'expected_output'</span>, <span class="hljs-string">''</span>)}</span>
    """</span>

    <span class="hljs-keyword">return</span> {
        <span class="hljs-string">"messages"</span>: [AIMessage(delegation_message)],
        <span class="hljs-string">"current_task_id"</span>: next_task.get(<span class="hljs-string">"id"</span>),
        <span class="hljs-string">"task_status"</span>: updated_status,
        <span class="hljs-string">"active_agent"</span>: agent_name
    }

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">specialized_agent_node</span><span class="hljs-params">(agent_name: str)</span>:</span>
    <span class="hljs-string">"""
    Factory function to create specialized agent nodes.
    """</span>
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">agent_node</span><span class="hljs-params">(state: SupervisorAgentState, config: RunnableConfig)</span> -&gt; Dict[str, Any]:</span>
        configuration = Configuration.from_runnable_config(config)

        <span class="hljs-comment"># Get agent configuration</span>
        agents = create_specialized_agents(tools_by_name)
        agent_config = agents.get(agent_name)

        <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> agent_config:
            <span class="hljs-keyword">return</span> {
                <span class="hljs-string">"messages"</span>: [AIMessage(<span class="hljs-string">f"Error: Agent <span class="hljs-subst">{agent_name}</span> not found"</span>)],
                <span class="hljs-string">"error_context"</span>: {<span class="hljs-string">"agent"</span>: agent_name, <span class="hljs-string">"error"</span>: <span class="hljs-string">"Agent not found"</span>}
            }

        <span class="hljs-comment"># Get current task context</span>
        current_task_id = state.get(<span class="hljs-string">"current_task_id"</span>)
        execution_plan = state.get(<span class="hljs-string">"execution_plan"</span>, {})
        current_task = <span class="hljs-literal">None</span>

        <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> execution_plan.get(<span class="hljs-string">"tasks"</span>, []):
            <span class="hljs-keyword">if</span> task.get(<span class="hljs-string">"id"</span>) == current_task_id:
                current_task = task
                <span class="hljs-keyword">break</span>

        <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> current_task:
            <span class="hljs-keyword">return</span> {
                <span class="hljs-string">"messages"</span>: [AIMessage(<span class="hljs-string">f"Error: No current task found for <span class="hljs-subst">{agent_name}</span>"</span>)],
                <span class="hljs-string">"error_context"</span>: {<span class="hljs-string">"agent"</span>: agent_name, <span class="hljs-string">"error"</span>: <span class="hljs-string">"No current task"</span>}
            }

        <span class="hljs-comment"># Create agent-specific system prompt</span>
        system_prompt = agent_config[<span class="hljs-string">"prompt"</span>].format(
            table_summary=state.get(<span class="hljs-string">"table_summary"</span>, <span class="hljs-string">""</span>),
            current_task=current_task.get(<span class="hljs-string">"description"</span>, <span class="hljs-string">""</span>),
            task_context=current_task.get(<span class="hljs-string">"context"</span>, {}),
            expected_output=current_task.get(<span class="hljs-string">"expected_output"</span>, <span class="hljs-string">""</span>)
        )

        <span class="hljs-comment"># Prepare messages for agent</span>
        messages = [
            SystemMessage(system_prompt),
            *state.get(<span class="hljs-string">"messages"</span>, [])[<span class="hljs-number">-3</span>:]  <span class="hljs-comment"># Last 3 messages for context</span>
        ]

        <span class="hljs-keyword">try</span>:
            <span class="hljs-comment"># Call specialized agent</span>
            model = agent_config[<span class="hljs-string">"model"</span>]
            response = model.invoke(messages, config)

            <span class="hljs-comment"># Check if agent made tool calls</span>
            <span class="hljs-keyword">if</span> hasattr(response, <span class="hljs-string">'tool_calls'</span>) <span class="hljs-keyword">and</span> response.tool_calls:
                <span class="hljs-comment"># Execute tools</span>
                tool_outputs = []
                <span class="hljs-keyword">for</span> tool_call <span class="hljs-keyword">in</span> response.tool_calls:
                    <span class="hljs-keyword">try</span>:
                        tool = tools_by_name[tool_call[<span class="hljs-string">"name"</span>]]
                        tool_result = tool.invoke(tool_call[<span class="hljs-string">"args"</span>])
                        tool_outputs.append(ToolMessage(
                            content=str(tool_result),
                            name=tool_call[<span class="hljs-string">"name"</span>],
                            tool_call_id=tool_call[<span class="hljs-string">"id"</span>]
                        ))
                    <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
                        tool_outputs.append(ToolMessage(
                            content=<span class="hljs-string">f"Error: <span class="hljs-subst">{str(e)}</span>"</span>,
                            name=tool_call[<span class="hljs-string">"name"</span>],
                            tool_call_id=tool_call[<span class="hljs-string">"id"</span>]
                        ))

                <span class="hljs-comment"># Update task status to completed</span>
                task_status = state.get(<span class="hljs-string">"task_status"</span>, [])
                <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> task_status:
                    <span class="hljs-keyword">if</span> task.get(<span class="hljs-string">"id"</span>) == current_task_id:
                        task[<span class="hljs-string">"status"</span>] = <span class="hljs-string">"completed"</span>
                        <span class="hljs-keyword">break</span>

                <span class="hljs-keyword">return</span> {
                    <span class="hljs-string">"messages"</span>: [response] + tool_outputs,
                    <span class="hljs-string">"task_status"</span>: task_status,
                    <span class="hljs-string">"agent_outputs"</span>: {agent_name: tool_outputs}
                }
            <span class="hljs-keyword">else</span>:
                <span class="hljs-comment"># Agent provided response without tools</span>
                <span class="hljs-keyword">return</span> {
                    <span class="hljs-string">"messages"</span>: [response],
                    <span class="hljs-string">"agent_outputs"</span>: {agent_name: response.content}
                }

        <span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
            <span class="hljs-comment"># Handle agent execution error</span>
            error_message = <span class="hljs-string">f"Agent <span class="hljs-subst">{agent_name}</span> execution failed: <span class="hljs-subst">{str(e)}</span>"</span>

            <span class="hljs-comment"># Update task status to failed</span>
            task_status = state.get(<span class="hljs-string">"task_status"</span>, [])
            <span class="hljs-keyword">for</span> task <span class="hljs-keyword">in</span> task_status:
                <span class="hljs-keyword">if</span> task.get(<span class="hljs-string">"id"</span>) == current_task_id:
                    task[<span class="hljs-string">"status"</span>] = <span class="hljs-string">"failed"</span>
                    task[<span class="hljs-string">"error"</span>] = str(e)
                    <span class="hljs-keyword">break</span>

            <span class="hljs-keyword">return</span> {
                <span class="hljs-string">"messages"</span>: [AIMessage(error_message)],
                <span class="hljs-string">"task_status"</span>: task_status,
                <span class="hljs-string">"error_context"</span>: {<span class="hljs-string">"agent"</span>: agent_name, <span class="hljs-string">"error"</span>: str(e)}
            }

    <span class="hljs-keyword">return</span> agent_node

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">should_continue_supervisor</span><span class="hljs-params">(state: SupervisorAgentState)</span> -&gt; str:</span>
    <span class="hljs-string">"""
    Determine the next node in the supervisor workflow.
    """</span>
    active_agent = state.get(<span class="hljs-string">"active_agent"</span>)

    <span class="hljs-keyword">if</span> active_agent == <span class="hljs-string">"planner"</span>:
        <span class="hljs-keyword">return</span> <span class="hljs-string">"planner"</span>
    <span class="hljs-keyword">elif</span> active_agent <span class="hljs-keyword">in</span> [<span class="hljs-string">"research_agent"</span>, <span class="hljs-string">"enrichment_agent"</span>, <span class="hljs-string">"content_agent"</span>,
                         <span class="hljs-string">"data_management_agent"</span>, <span class="hljs-string">"execution_agent"</span>]:
        <span class="hljs-keyword">return</span> active_agent
    <span class="hljs-keyword">elif</span> active_agent <span class="hljs-keyword">is</span> <span class="hljs-literal">None</span>:
        <span class="hljs-keyword">return</span> END
    <span class="hljs-keyword">else</span>:
        <span class="hljs-keyword">return</span> <span class="hljs-string">"supervisor"</span>

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_supervisor_graph</span><span class="hljs-params">()</span> -&gt; StateGraph:</span>
    <span class="hljs-string">"""
    Create the complete supervisor agentic pattern graph.
    """</span>
    <span class="hljs-comment"># Create the graph with enhanced state</span>
    workflow = StateGraph(SupervisorAgentState, config_schema=Configuration)

    <span class="hljs-comment"># Add nodes</span>
    workflow.add_node(<span class="hljs-string">"table_indexing"</span>, table_indexing_node)  <span class="hljs-comment"># Preserved from original</span>
    workflow.add_node(<span class="hljs-string">"supervisor"</span>, supervisor_node)
    workflow.add_node(<span class="hljs-string">"planner"</span>, planner_node)

    <span class="hljs-comment"># Add specialized agent nodes</span>
    workflow.add_node(<span class="hljs-string">"research_agent"</span>, specialized_agent_node(<span class="hljs-string">"research_agent"</span>))
    workflow.add_node(<span class="hljs-string">"enrichment_agent"</span>, specialized_agent_node(<span class="hljs-string">"enrichment_agent"</span>))
    workflow.add_node(<span class="hljs-string">"content_agent"</span>, specialized_agent_node(<span class="hljs-string">"content_agent"</span>))
    workflow.add_node(<span class="hljs-string">"data_management_agent"</span>, specialized_agent_node(<span class="hljs-string">"data_management_agent"</span>))
    workflow.add_node(<span class="hljs-string">"execution_agent"</span>, specialized_agent_node(<span class="hljs-string">"execution_agent"</span>))

    <span class="hljs-comment"># Define workflow edges</span>
    workflow.set_entry_point(<span class="hljs-string">"table_indexing"</span>)
    workflow.add_edge(<span class="hljs-string">"table_indexing"</span>, <span class="hljs-string">"supervisor"</span>)

    <span class="hljs-comment"># Conditional edges from supervisor</span>
    workflow.add_conditional_edges(
        <span class="hljs-string">"supervisor"</span>,
        should_continue_supervisor,
        {
            <span class="hljs-string">"planner"</span>: <span class="hljs-string">"planner"</span>,
            <span class="hljs-string">"research_agent"</span>: <span class="hljs-string">"research_agent"</span>,
            <span class="hljs-string">"enrichment_agent"</span>: <span class="hljs-string">"enrichment_agent"</span>,
            <span class="hljs-string">"content_agent"</span>: <span class="hljs-string">"content_agent"</span>,
            <span class="hljs-string">"data_management_agent"</span>: <span class="hljs-string">"data_management_agent"</span>,
            <span class="hljs-string">"execution_agent"</span>: <span class="hljs-string">"execution_agent"</span>,
            END: END
        }
    )

    <span class="hljs-comment"># All specialized agents return to supervisor</span>
    workflow.add_edge(<span class="hljs-string">"planner"</span>, <span class="hljs-string">"supervisor"</span>)
    workflow.add_edge(<span class="hljs-string">"research_agent"</span>, <span class="hljs-string">"supervisor"</span>)
    workflow.add_edge(<span class="hljs-string">"enrichment_agent"</span>, <span class="hljs-string">"supervisor"</span>)
    workflow.add_edge(<span class="hljs-string">"content_agent"</span>, <span class="hljs-string">"supervisor"</span>)
    workflow.add_edge(<span class="hljs-string">"data_management_agent"</span>, <span class="hljs-string">"supervisor"</span>)
    workflow.add_edge(<span class="hljs-string">"execution_agent"</span>, <span class="hljs-string">"supervisor"</span>)

    <span class="hljs-comment"># Compile the graph</span>
    graph = workflow.compile()
    graph.name = <span class="hljs-string">"Supervisor Agentic Pattern"</span>

    <span class="hljs-keyword">return</span> graph

<span class="hljs-comment"># Create the supervisor graph instance</span>
supervisor_graph = create_supervisor_graph()
</div></code></pre>
<h2 id="sequence-diagrams---supervisor-to-agent-task-flows">Sequence Diagrams - Supervisor-to-Agent Task Flows</h2>
<h3 id="scenario-1-complex-prospect-research-and-enrichment">Scenario 1: Complex Prospect Research and Enrichment</h3>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant Research
    participant Enrichment
    participant Content
    participant Execution

    User->>Supervisor: "Find 50 marketing managers at SaaS companies and create personalized outreach"
    Supervisor->>Planner: Create execution plan
    Planner-->>Supervisor: Plan with 5 tasks across 4 agents

    Supervisor->>Research: Task 1: Find LinkedIn profiles
    Research->>Research: search_linkedin_profiles
    Research-->>Supervisor: 50 profiles discovered

    Supervisor->>Enrichment: Task 2: Import LinkedIn profiles
    Enrichment->>Enrichment: upsert_linkedin_person_profile_column_from_url
    Enrichment-->>Supervisor: Profile column created

    Supervisor->>Enrichment: Task 3: Discover work emails
    Enrichment->>Enrichment: upsert_work_email_column
    Enrichment-->>Supervisor: Email column created

    Supervisor->>Content: Task 4: Create personalized messages
    Content->>Content: upsert_ai_message_copywriter
    Content-->>Supervisor: Message column created

    Supervisor->>Execution: Task 5: Execute all columns
    Execution->>User: Confirm execution for 50 rows?
    User-->>Execution: Confirmed
    Execution->>Execution: run_column (multiple)
    Execution-->>Supervisor: All columns executed successfully

    Supervisor->>User: "Campaign ready: 50 prospects with personalized messages"
</div></code></pre>
<h3 id="scenario-2-data-analysis-and-filtering">Scenario 2: Data Analysis and Filtering</h3>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant Supervisor
    participant Planner
    participant DataMgmt
    participant Research

    User->>Supervisor: "Show me prospects from companies with 100+ employees in tech industry"
    Supervisor->>Planner: Create analysis plan
    Planner-->>Supervisor: Plan with data filtering and research validation

    Supervisor->>DataMgmt: Task 1: Analyze current table state
    DataMgmt->>DataMgmt: read_user_view_table_filters
    DataMgmt->>DataMgmt: read_table_data (summary)
    DataMgmt-->>Supervisor: Current state analysis

    Supervisor->>DataMgmt: Task 2: Apply company size filters
    DataMgmt->>DataMgmt: update_user_view_table_filters_tool
    DataMgmt-->>Supervisor: Filters applied

    Supervisor->>Research: Task 3: Validate company information
    Research->>Research: search (company verification)
    Research-->>Supervisor: Company data validated

    Supervisor->>DataMgmt: Task 4: Generate filtered dataset
    DataMgmt->>DataMgmt: read_table_data (filtered)
    DataMgmt-->>Supervisor: Filtered results ready

    Supervisor->>User: "Found 23 prospects from tech companies 100+ employees"
</div></code></pre>
<h2 id="backward-compatibility-and-migration">Backward Compatibility and Migration</h2>
<h3 id="preserving-existing-features">Preserving Existing Features</h3>
<pre class="hljs"><code><div><span class="hljs-comment"># Compatibility layer for existing implementations</span>
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">CompatibilityLayer</span>:</span>
    <span class="hljs-string">"""
    Ensures backward compatibility with existing single-agent implementation.
    """</span>

<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">migrate_existing_state</span><span class="hljs-params">(old_state: AgentState)</span> -&gt; SupervisorAgentState:</span>
        <span class="hljs-string">"""Migrate existing AgentState to SupervisorAgentState."""</span>
        <span class="hljs-keyword">return</span> SupervisorAgentState(
            messages=old_state.get(<span class="hljs-string">"messages"</span>, []),
            table_summary=old_state.get(<span class="hljs-string">"table_summary"</span>),
            mode=old_state.get(<span class="hljs-string">"mode"</span>),
            selected_row_ids=old_state.get(<span class="hljs-string">"selected_row_ids"</span>),
            selected_column_ids=old_state.get(<span class="hljs-string">"selected_column_ids"</span>),
            <span class="hljs-comment"># Initialize supervisor-specific fields</span>
            execution_plan=<span class="hljs-literal">None</span>,
            current_task_id=<span class="hljs-literal">None</span>,
            task_status=<span class="hljs-literal">None</span>,
            active_agent=<span class="hljs-literal">None</span>,
            delegation_history=<span class="hljs-literal">None</span>,
            agent_outputs=<span class="hljs-literal">None</span>,
            pending_confirmations=<span class="hljs-literal">None</span>,
            error_context=<span class="hljs-literal">None</span>
        )

<span class="hljs-meta">    @staticmethod</span>
    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">fallback_to_single_agent</span><span class="hljs-params">(state: SupervisorAgentState)</span> -&gt; bool:</span>
        <span class="hljs-string">"""
        Determine if request should fallback to single-agent mode.
        """</span>
        <span class="hljs-comment"># Simple requests that don't require multi-agent coordination</span>
        messages = state.get(<span class="hljs-string">"messages"</span>, [])
        <span class="hljs-keyword">if</span> <span class="hljs-keyword">not</span> messages:
            <span class="hljs-keyword">return</span> <span class="hljs-literal">True</span>

        last_message = messages[<span class="hljs-number">-1</span>]
        <span class="hljs-keyword">if</span> hasattr(last_message, <span class="hljs-string">'content'</span>):
            content = last_message.content.lower()

            <span class="hljs-comment"># Single-tool operations</span>
            simple_patterns = [
                <span class="hljs-string">"read table"</span>, <span class="hljs-string">"show data"</span>, <span class="hljs-string">"what's in"</span>, <span class="hljs-string">"filter by"</span>,
                <span class="hljs-string">"run column"</span>, <span class="hljs-string">"execute"</span>, <span class="hljs-string">"search for"</span>, <span class="hljs-string">"scrape"</span>
            ]

            <span class="hljs-keyword">if</span> any(pattern <span class="hljs-keyword">in</span> content <span class="hljs-keyword">for</span> pattern <span class="hljs-keyword">in</span> simple_patterns):
                <span class="hljs-keyword">return</span> <span class="hljs-literal">True</span>

        <span class="hljs-keyword">return</span> <span class="hljs-literal">False</span>

<span class="hljs-comment"># Configuration flag for gradual migration</span>
ENABLE_SUPERVISOR_PATTERN = <span class="hljs-literal">True</span>

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_adaptive_graph</span><span class="hljs-params">()</span> -&gt; StateGraph:</span>
    <span class="hljs-string">"""
    Create graph that can operate in both single-agent and supervisor modes.
    """</span>
    <span class="hljs-keyword">if</span> ENABLE_SUPERVISOR_PATTERN:
        <span class="hljs-keyword">return</span> create_supervisor_graph()
    <span class="hljs-keyword">else</span>:
        <span class="hljs-comment"># Fallback to original single-agent implementation</span>
        <span class="hljs-keyword">return</span> create_graph()  <span class="hljs-comment"># Original implementation</span>
</div></code></pre>
<h3 id="error-handling-and-user-experience-preservation">Error Handling and User Experience Preservation</h3>
<pre class="hljs"><code><div><span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">preserve_user_confirmations</span><span class="hljs-params">(state: SupervisorAgentState, config: RunnableConfig)</span> -&gt; Dict[str, Any]:</span>
    <span class="hljs-string">"""
    Ensure user confirmations are preserved across agent transitions.
    """</span>
    pending_confirmations = state.get(<span class="hljs-string">"pending_confirmations"</span>, [])

    <span class="hljs-keyword">if</span> pending_confirmations:
        <span class="hljs-comment"># Handle pending confirmations before proceeding</span>
        <span class="hljs-keyword">for</span> confirmation <span class="hljs-keyword">in</span> pending_confirmations:
            <span class="hljs-keyword">if</span> confirmation.get(<span class="hljs-string">"type"</span>) == <span class="hljs-string">"run_column"</span>:
                <span class="hljs-comment"># Use existing interrupt mechanism</span>
                user_response = interrupt(confirmation.get(<span class="hljs-string">"message"</span>))

                <span class="hljs-keyword">if</span> user_response <span class="hljs-keyword">and</span> str(user_response).lower() <span class="hljs-keyword">in</span> [<span class="hljs-string">'yes'</span>, <span class="hljs-string">'y'</span>, <span class="hljs-string">'confirm'</span>, <span class="hljs-string">'proceed'</span>, <span class="hljs-string">'ok'</span>, <span class="hljs-string">'run'</span>]:
                    <span class="hljs-comment"># User confirmed, proceed with execution</span>
                    confirmation[<span class="hljs-string">"status"</span>] = <span class="hljs-string">"confirmed"</span>
                <span class="hljs-keyword">else</span>:
                    <span class="hljs-comment"># User cancelled, update task status</span>
                    confirmation[<span class="hljs-string">"status"</span>] = <span class="hljs-string">"cancelled"</span>

    <span class="hljs-keyword">return</span> {<span class="hljs-string">"pending_confirmations"</span>: pending_confirmations}

<span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">maintain_stream_writing</span><span class="hljs-params">(agent_name: str, action: str)</span> -&gt; <span class="hljs-keyword">None</span>:</span>
    <span class="hljs-string">"""
    Preserve real-time status updates across all agents.
    """</span>
    <span class="hljs-keyword">from</span> langgraph.config <span class="hljs-keyword">import</span> get_stream_writer

    stream_writer = get_stream_writer()
    stream_writer({
        <span class="hljs-string">"custom_tool_call"</span>: <span class="hljs-string">f"[<span class="hljs-subst">{agent_name}</span>] <span class="hljs-subst">{action}</span>"</span>,
        <span class="hljs-string">"agent"</span>: agent_name,
        <span class="hljs-string">"timestamp"</span>: datetime.now().isoformat()
    })
</div></code></pre>
<h2 id="performance-optimizations-and-monitoring">Performance Optimizations and Monitoring</h2>
<h3 id="execution-metrics">Execution Metrics</h3>
<pre class="hljs"><code><div><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">SupervisorMetrics</span>:</span>
    <span class="hljs-string">"""
    Performance monitoring for supervisor agentic pattern.
    """</span>

    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">__init__</span><span class="hljs-params">(self)</span>:</span>
        self.task_execution_times = {}
        self.agent_performance = {}
        self.error_rates = {}
        self.user_satisfaction = {}

    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">track_task_execution</span><span class="hljs-params">(self, task_id: str, agent: str, duration: float, success: bool)</span>:</span>
        <span class="hljs-string">"""Track individual task performance."""</span>
        <span class="hljs-keyword">if</span> agent <span class="hljs-keyword">not</span> <span class="hljs-keyword">in</span> self.agent_performance:
            self.agent_performance[agent] = {
                <span class="hljs-string">"total_tasks"</span>: <span class="hljs-number">0</span>,
                <span class="hljs-string">"successful_tasks"</span>: <span class="hljs-number">0</span>,
                <span class="hljs-string">"average_duration"</span>: <span class="hljs-number">0</span>,
                <span class="hljs-string">"error_count"</span>: <span class="hljs-number">0</span>
            }

        metrics = self.agent_performance[agent]
        metrics[<span class="hljs-string">"total_tasks"</span>] += <span class="hljs-number">1</span>
        <span class="hljs-keyword">if</span> success:
            metrics[<span class="hljs-string">"successful_tasks"</span>] += <span class="hljs-number">1</span>
        <span class="hljs-keyword">else</span>:
            metrics[<span class="hljs-string">"error_count"</span>] += <span class="hljs-number">1</span>

        <span class="hljs-comment"># Update average duration</span>
        current_avg = metrics[<span class="hljs-string">"average_duration"</span>]
        total_tasks = metrics[<span class="hljs-string">"total_tasks"</span>]
        metrics[<span class="hljs-string">"average_duration"</span>] = ((current_avg * (total_tasks - <span class="hljs-number">1</span>)) + duration) / total_tasks

    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">get_performance_report</span><span class="hljs-params">(self)</span> -&gt; Dict[str, Any]:</span>
        <span class="hljs-string">"""Generate comprehensive performance report."""</span>
        <span class="hljs-keyword">return</span> {
            <span class="hljs-string">"agent_performance"</span>: self.agent_performance,
            <span class="hljs-string">"total_tasks_executed"</span>: sum(m[<span class="hljs-string">"total_tasks"</span>] <span class="hljs-keyword">for</span> m <span class="hljs-keyword">in</span> self.agent_performance.values()),
            <span class="hljs-string">"overall_success_rate"</span>: sum(m[<span class="hljs-string">"successful_tasks"</span>] <span class="hljs-keyword">for</span> m <span class="hljs-keyword">in</span> self.agent_performance.values()) /
                                  max(sum(m[<span class="hljs-string">"total_tasks"</span>] <span class="hljs-keyword">for</span> m <span class="hljs-keyword">in</span> self.agent_performance.values()), <span class="hljs-number">1</span>),
            <span class="hljs-string">"average_execution_time"</span>: sum(m[<span class="hljs-string">"average_duration"</span>] <span class="hljs-keyword">for</span> m <span class="hljs-keyword">in</span> self.agent_performance.values()) /
                                    max(len(self.agent_performance), <span class="hljs-number">1</span>)
        }

<span class="hljs-comment"># Global metrics instance</span>
supervisor_metrics = SupervisorMetrics()
</div></code></pre>
<h2 id="summary">Summary</h2>
<p>This Supervisor Agentic Pattern implementation provides:</p>
<ol>
<li><strong>Complete Tool Coverage</strong>: All 16 existing tools mapped to specialized agents</li>
<li><strong>Backward Compatibility</strong>: Seamless migration from single-agent to multi-agent pattern</li>
<li><strong>Enhanced Planning</strong>: Sophisticated task breakdown and dependency management</li>
<li><strong>Robust Error Handling</strong>: Comprehensive error recovery and user communication</li>
<li><strong>Performance Monitoring</strong>: Detailed metrics and optimization capabilities</li>
<li><strong>User Experience Preservation</strong>: All existing confirmations and real-time updates maintained</li>
</ol>
<p>The pattern enables complex multi-step workflows while maintaining the simplicity and reliability of the original single-agent implementation for straightforward tasks.</p>
<pre class="hljs"><code><div></div></code></pre>
<pre class="hljs"><code><div></div></code></pre>

</body>
</html>
