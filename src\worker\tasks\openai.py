from src.schemas.requests import ServiceRequest
from ..celery_app import app
from src.services.llm.generation import (
    run_ai_formula,
    run_ai_column,
    run_research_agent,
)
from supabase import AsyncClient
from typing import Dict, Any
import logging

# Import centralized configuration
from src.core.config import get_settings
import asyncio
from src.db.utils import get_supabase_client

# Get application settings
settings = get_settings()

logger = logging.getLogger("backend_api.tasks.openai")


@app.task(
    name="src.worker.tasks.openai.run_openai_task",
    ignore_result=True,
    max_retries=1,
    queue="llm",
    rate_limit="10000/m",
    bind=True,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={"max_retries": 1},
)
def run_openai_task(self, request):
    """Celery task to run OpenAI requests.

    Note: Celery doesn't support async functions directly as tasks.
    We convert the dict back to the appropriate request object.

    Args:
        self: The Celery task instance
        request_dict: Dictionary representation of the request
    """
    try:
        input = ServiceRequest(**request)
        asyncio.run(_run_openai_task(input))

    except Exception as e:
        logger.error(f"Error in run_openai_task: {str(e)}")
        # raise  # Re-raise to trigger Celery retry


async def _run_openai_task(request: ServiceRequest):
    supabase_client: AsyncClient = await get_supabase_client()
    #
    match request.service_id:
        case 1 | 2 | 3 | 4 | 5 | 6 | 14 | 18:
            await run_ai_formula(request, supabase_client)
        case 13:
            await run_ai_column(request, supabase_client)
        case 12 | 19 | 20 | 21 | 22:
            await run_research_agent(request, supabase_client)
        case _:
            logger.error(f"Unknown service_id: {request.service_id}")
