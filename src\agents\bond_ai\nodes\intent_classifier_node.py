from typing import List
from langchain_core.messages import SystemMessage, HumanMessage

from bond_ai.utils import clean_thinking_blocks_for_bedrock
from bond_ai.configuration import Configuration
from bond_ai.utils import load_chat_model
from bond_ai.state import AgentState

INTENT_CLASSIFICATION_PROMPT = """Analyze the user's message and classify their intent. Return only the category names that apply (comma-separated).

Categories:
- data_exploration: viewing, showing, displaying table data
- web_research: searching web, scraping websites  
- profile_enrichment: adding LinkedIn profiles, emails, phone numbers
- content_generation: creating AI columns, writing copy, research
- data_manipulation: creating/editing columns, updating filters
- execution: running columns, executing workflows
- prospecting: finding LinkedIn profiles, lead generation

User message: {user_message}

Return only category names (e.g., "data_exploration, content_generation"):"""

def intent_classifier_node(state: AgentState, config):
  
    """Classify user intent to determine relevant tool categories"""
    configuration = Configuration.from_runnable_config(config)

    # Get the last user message
    last_message = None
    for msg in reversed(state["messages"]):
        if hasattr(msg, 'type') and msg.type == 'human':
            last_message = msg.content
            break
    
    if not last_message:
        # Default categories if no user message found
        return {"intents": ["data_exploration", "content_generation"]}
    
    # Use a lightweight model for classification
    model = load_chat_model(configuration.model)  # Fast, cheap model

    cleaned_messages = clean_thinking_blocks_for_bedrock(list(state["messages"]))
    messages = [
        SystemMessage(content=INTENT_CLASSIFICATION_PROMPT.format(user_message=last_message)),
        *cleaned_messages
    ]
    response = model.invoke(messages, config)
    print(f"Intent classification response: {response}")
    
    # Parse categories from response
    try:
        # Extract content from the response
        content = ""
        if hasattr(response, 'content'):
            if isinstance(response.content, str):
                content = response.content
            elif isinstance(response.content, list):
                # Extract only text content, ignore thinking blocks
                text_parts = []
                for item in response.content:
                    if isinstance(item, dict) and item.get('type') == 'text':
                        text_parts.append(item.get('text', ''))
                    elif isinstance(item, str):
                        text_parts.append(item)
                content = ' '.join(text_parts)
            else:
                content = str(response.content)
        else:
            content = str(response)
        
        # Parse categories from response
        if content:
            categories = [cat.strip() for cat in content.split(",") if cat.strip()]
        else:
            categories = ["data_exploration", "content_generation"]  # fallback
            
        # Validate categories against known ones
        valid_categories = {
            "data_exploration", "web_research", "profile_enrichment", 
            "content_generation", "data_manipulation", "execution", "prospecting"
        }
        
        # Filter to only valid categories
        filtered_categories = [cat for cat in categories if cat in valid_categories]
        
        # Ensure we always return at least one category
        if not filtered_categories:
            filtered_categories = ["data_exploration", "content_generation"]

        # Return only intents for state update
        return {
            "intents": filtered_categories
        }
        
    except Exception as e:
        print(f"Error parsing intent classification response: {e}")
        # Fallback to default categories
        default_categories = ["data_exploration", "content_generation"]
        return {
            "intents": default_categories
        }
