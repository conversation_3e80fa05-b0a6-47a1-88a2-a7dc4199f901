<!DOCTYPE html>
<html>
<head>
<title>outbond_ai_assistant_technical_documentation.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="outbond-ai-assistant---technical-documentation">Outbond AI Assistant - Technical Documentation</h1>
<h2 id="overview">Overview</h2>
<p>The Outbond AI Assistant is a LangGraph-based ReAct (Reasoning and Acting) agent designed to help users manage outbound sales campaigns within Outbond's no-code workspace. The agent operates on table data and provides intelligent automation for prospect research, data enrichment, and outreach campaign management.</p>
<h2 id="architecture">Architecture</h2>
<h3 id="core-components">Core Components</h3>
<ul>
<li><strong>Graph Implementation</strong>: <code>src/outbond_ai_assistant/graph.py</code></li>
<li><strong>State Management</strong>: <code>src/outbond_ai_assistant/state.py</code></li>
<li><strong>Tools</strong>: <code>src/outbond_ai_assistant/tools.py</code></li>
<li><strong>Configuration</strong>: <code>src/outbond_ai_assistant/configuration.py</code></li>
<li><strong>Prompts</strong>: <code>src/outbond_ai_assistant/prompts.py</code></li>
</ul>
<h2 id="agent-logic-analysis">Agent Logic Analysis</h2>
<h3 id="state-management-agentstate">State Management (<code>AgentState</code>)</h3>
<p>The agent uses a TypedDict-based state structure with the following key components:</p>
<pre class="hljs"><code><div><span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">AgentState</span><span class="hljs-params">(TypedDict)</span>:</span>
    messages: Annotated[Sequence[BaseMessage], add_messages]
    table_summary: Annotated[Optional[str], preserve_table_summary]
    mode: Optional[str]
    selected_row_ids: Optional[int]
    selected_column_ids: Optional[str]
</div></code></pre>
<p><strong>State Fields:</strong></p>
<ul>
<li><code>messages</code>: Conversation history with automatic message aggregation</li>
<li><code>table_summary</code>: Cached table schema and data summary for efficient operations</li>
<li><code>mode</code>: Operating mode (&quot;chat&quot; for conversational mode, None for tool mode)</li>
<li><code>selected_row_ids</code>: User-selected table rows for focused operations</li>
<li><code>selected_column_ids</code>: User-selected table columns for targeted actions</li>
</ul>
<h3 id="workflow-nodes">Workflow Nodes</h3>
<h4 id="1-table-indexing-node-tableindexingnode">1. Table Indexing Node (<code>table_indexing_node</code>)</h4>
<p><strong>Purpose</strong>: Analyzes table structure and creates intelligent summaries for agent context.</p>
<p><strong>Responsibilities:</strong></p>
<ul>
<li>Retrieves table column metadata</li>
<li>Generates AI-powered column descriptions for columns lacking summaries</li>
<li>Creates comprehensive table schema with data insights</li>
<li>Updates column descriptions in the database</li>
<li>Provides fresh table summary for agent operations</li>
</ul>
<p><strong>Key Features:</strong></p>
<ul>
<li>Only processes columns without existing descriptions (efficiency optimization)</li>
<li>Uses structured analysis with 10-row samples</li>
<li>Combines fresh schema data with stored summaries</li>
<li>Validates all columns have descriptions before proceeding</li>
</ul>
<h4 id="2-model-call-node-callmodel">2. Model Call Node (<code>call_model</code>)</h4>
<p><strong>Purpose</strong>: Orchestrates LLM interactions with context-aware prompt engineering.</p>
<p><strong>Responsibilities:</strong></p>
<ul>
<li>Loads appropriate chat model based on configuration</li>
<li>Handles chat mode vs tool mode differentiation</li>
<li>Constructs context-rich system prompts</li>
<li>Manages selected rows/columns context</li>
<li>Implements error handling with graceful fallbacks</li>
</ul>
<p><strong>Context Management:</strong></p>
<ul>
<li>Injects table summary, current filters, and selected data context</li>
<li>Cleans thinking blocks for Bedrock compatibility</li>
<li>Adds mode-specific instructions (chat vs tool mode)</li>
</ul>
<h4 id="3-tool-execution-node-toolnode">3. Tool Execution Node (<code>tool_node</code>)</h4>
<p><strong>Purpose</strong>: Executes tools based on model decisions with user confirmation for critical actions.</p>
<p><strong>Responsibilities:</strong></p>
<ul>
<li>Processes tool calls from AI messages</li>
<li>Implements user confirmation for <code>run_column</code> operations</li>
<li>Executes tools with comprehensive error handling</li>
<li>Returns structured tool results</li>
</ul>
<p><strong>Special Features:</strong></p>
<ul>
<li>Interactive confirmation for column execution with row count details</li>
<li>Graceful error handling with detailed error messages</li>
<li>Support for multiple tool calls in sequence</li>
</ul>
<h3 id="edge-conditions-and-routing-logic">Edge Conditions and Routing Logic</h3>
<h4 id="conditional-edge-shouldcontinue">Conditional Edge (<code>should_continue</code>)</h4>
<p><strong>Decision Logic:</strong></p>
<ol>
<li><strong>Chat Mode</strong>: Always routes to END (no tool execution)</li>
<li><strong>Tool Calls Present</strong>: Routes to &quot;tools&quot; node for execution</li>
<li><strong>No Tool Calls</strong>: Routes to END (conversation complete)</li>
</ol>
<p><strong>Flow Diagram:</strong></p>
<pre class="hljs"><code><div>table_indexing → agent → [should_continue] → tools → agent
                      ↓                           ↑
                     END ←─────────────────────────┘
</div></code></pre>
<h3 id="error-handling-and-fallback-mechanisms">Error Handling and Fallback Mechanisms</h3>
<ol>
<li><strong>Table Indexing Failures</strong>: Comprehensive exception handling with detailed error messages</li>
<li><strong>Model Call Failures</strong>: Automatic fallback with cleaned message history</li>
<li><strong>Tool Execution Failures</strong>: Individual tool error capture with continued execution</li>
<li><strong>User Cancellation</strong>: Graceful handling of cancelled operations</li>
</ol>
<h2 id="tool-documentation">Tool Documentation</h2>
<h3 id="core-tools-overview">Core Tools Overview</h3>
<p>The agent has access to 16 specialized tools organized into categories:</p>
<h4 id="data-access-tools">Data Access Tools</h4>
<ol>
<li><strong><code>read_table_data</code></strong>: Fetches table data with filtering, sorting, and summarization</li>
<li><strong><code>read_user_view_table_filters</code></strong>: Retrieves current table view filters</li>
<li><strong><code>update_user_view_table_filters_tool</code></strong>: Updates table view filters</li>
</ol>
<h4 id="web-research-tools">Web Research Tools</h4>
<ol start="4">
<li><strong><code>search</code></strong>: General web search using Tavily search engine</li>
<li><strong><code>scrape_website</code></strong>: Website content extraction using FireCrawl</li>
</ol>
<h4 id="linkedin-integration-tools">LinkedIn Integration Tools</h4>
<ol start="6">
<li><strong><code>search_linkedin_profiles</code></strong>: Bulk LinkedIn profile discovery</li>
<li><strong><code>upsert_linkedin_person_profile_column_from_url</code></strong>: Import LinkedIn person profiles</li>
<li><strong><code>upsert_linkedin_company_profile_column_from_url</code></strong>: Import LinkedIn company profiles</li>
</ol>
<h4 id="contact-discovery-tools">Contact Discovery Tools</h4>
<ol start="9">
<li><strong><code>upsert_phone_number_column</code></strong>: Phone number discovery from LinkedIn profiles</li>
<li><strong><code>upsert_work_email_column</code></strong>: Work email discovery using name and company domain</li>
</ol>
<h4 id="content-generation-tools">Content Generation Tools</h4>
<ol start="11">
<li><strong><code>upsert_text_column</code></strong>: Static text or formula-based columns</li>
<li><strong><code>upsert_ai_text_column</code></strong>: AI-generated content columns</li>
<li><strong><code>upsert_bond_ai_researcher_column</code></strong>: Prospect research insights</li>
<li><strong><code>upsert_ai_message_copywriter</code></strong>: Personalized outreach copy</li>
</ol>
<h4 id="execution-tools">Execution Tools</h4>
<ol start="15">
<li><strong><code>run_column</code></strong>: Execute smart columns with optional result waiting</li>
<li><strong><code>upsert_text_column</code></strong>: (Duplicate for legacy compatibility)</li>
</ol>
<h3 id="tool-integration-points">Tool Integration Points</h3>
<p><strong>Configuration Injection</strong>: All tools receive <code>RunnableConfig</code> with table_id and model settings
<strong>Stream Writing</strong>: Tools provide real-time status updates via <code>get_stream_writer()</code>
<strong>Validation</strong>: Input validation using Pydantic models and injection sequence validation
<strong>Error Handling</strong>: Consistent error response format across all tools</p>
<h3 id="tool-dependencies">Tool Dependencies</h3>
<ul>
<li><strong>Database Layer</strong>: <code>agent_db.py</code> functions for data persistence</li>
<li><strong>External APIs</strong>: Tavily (search), FireCrawl (scraping), LinkedIn APIs</li>
<li><strong>AI Models</strong>: OpenAI GPT-4o-mini for summarization, Bedrock Claude for main operations</li>
<li><strong>Validation Services</strong>: Injection sequence validation for data integrity</li>
</ul>
<h2 id="user-interaction-flow">User Interaction Flow</h2>
<h3 id="scenario-1-building-a-prospect-list">Scenario 1: Building a Prospect List</h3>
<p><strong>User Query</strong>: &quot;Help me find 50 marketing managers at SaaS companies in San Francisco&quot;</p>
<p><strong>Expected Flow:</strong></p>
<ol>
<li><strong>Table Indexing</strong>: Analyze current table structure</li>
<li><strong>Agent Planning</strong>: Understand requirements and plan LinkedIn search</li>
<li><strong>Tool Execution</strong>: <code>search_linkedin_profiles</code> with appropriate filters</li>
<li><strong>Profile Import</strong>: <code>upsert_linkedin_person_profile_column_from_url</code> for discovered profiles</li>
<li><strong>Data Enrichment</strong>: <code>upsert_work_email_column</code> and <code>upsert_phone_number_column</code></li>
<li><strong>Execution</strong>: <code>run_column</code> to populate the enrichment data</li>
</ol>
<h3 id="scenario-2-creating-personalized-outreach">Scenario 2: Creating Personalized Outreach</h3>
<p><strong>User Query</strong>: &quot;Create personalized LinkedIn messages for my prospects&quot;</p>
<p><strong>Expected Flow:</strong></p>
<ol>
<li><strong>Table Analysis</strong>: Review existing prospect data</li>
<li><strong>Research Enhancement</strong>: <code>upsert_bond_ai_researcher_column</code> for prospect insights</li>
<li><strong>Message Creation</strong>: <code>upsert_ai_message_copywriter</code> with personalization prompts</li>
<li><strong>Execution</strong>: <code>run_column</code> to generate personalized messages</li>
<li><strong>Review</strong>: Present results for user approval</li>
</ol>
<h3 id="scenario-3-data-filtering-and-analysis">Scenario 3: Data Filtering and Analysis</h3>
<p><strong>User Query</strong>: &quot;Show me only prospects from companies with 100+ employees&quot;</p>
<p><strong>Expected Flow:</strong></p>
<ol>
<li><strong>Current Filters</strong>: <code>read_user_view_table_filters</code> to understand current state</li>
<li><strong>Filter Update</strong>: <code>update_user_view_table_filters_tool</code> with company size criteria</li>
<li><strong>Data Review</strong>: <code>read_table_data</code> with applied filters for verification</li>
</ol>
<h2 id="configuration">Configuration</h2>
<h3 id="default-settings">Default Settings</h3>
<ul>
<li><strong>Table ID</strong>: <code>tbl_c7e3cfcc8a402da0</code></li>
<li><strong>Model</strong>: <code>bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0</code></li>
<li><strong>Max Search Results</strong>: 5 (configurable)</li>
</ul>
<h3 id="environment-variables">Environment Variables</h3>
<ul>
<li><code>FIRECRAWL_API_KEY</code>: For website scraping functionality</li>
<li><code>CRUSTDATA_API_KEY</code>: For company data enrichment</li>
</ul>
<h2 id="detailed-user-scenarios-with-sequence-diagrams">Detailed User Scenarios with Sequence Diagrams</h2>
<h3 id="scenario-1-building-a-prospect-list-detailed">Scenario 1: Building a Prospect List (Detailed)</h3>
<p><strong>User Query</strong>: &quot;Help me find 50 marketing managers at SaaS companies in San Francisco&quot;</p>
<p><strong>Step-by-Step Execution:</strong></p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant Agent
    participant TableIndexing
    participant LLM
    participant LinkedInTool
    participant Database

    User->>Agent: "Find 50 marketing managers at SaaS companies in SF"
    Agent->>TableIndexing: Analyze table structure
    TableIndexing->>Database: Get column metadata
    Database-->>TableIndexing: Column data
    TableIndexing->>LLM: Generate column summaries
    LLM-->>TableIndexing: AI-generated descriptions
    TableIndexing-->>Agent: Table summary with schema

    Agent->>LLM: Plan LinkedIn search strategy
    LLM-->>Agent: Tool call: search_linkedin_profiles
    Agent->>LinkedInTool: Execute search with filters
    LinkedInTool->>Database: Create profile columns
    LinkedInTool-->>Agent: 50 profiles found and saved

    Agent->>User: "Found 50 marketing managers. Would you like me to enrich with contact data?"
</div></code></pre>
<p><strong>Expected Tool Sequence:</strong></p>
<ol>
<li><code>search_linkedin_profiles(filters=[job_title=&quot;Marketing Manager&quot;, company_type=&quot;SaaS&quot;, location=&quot;San Francisco&quot;], page=1)</code></li>
<li><code>upsert_linkedin_person_profile_column_from_url(column_name=&quot;LinkedIn Profile&quot;, linkedin_profile_url=&quot;{{profile_url}}&quot;)</code></li>
<li><code>run_column(column_id=&quot;profile_column_id&quot;, count=50, wait_results=False)</code></li>
</ol>
<h3 id="scenario-2-email-discovery-and-outreach-creation">Scenario 2: Email Discovery and Outreach Creation</h3>
<p><strong>User Query</strong>: &quot;Find work emails for my prospects and create personalized outreach messages&quot;</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant Agent
    participant EmailTool
    participant ResearchTool
    participant CopywriterTool
    participant Database

    User->>Agent: "Find emails and create personalized messages"
    Agent->>EmailTool: Create work email column
    EmailTool->>Database: Setup email discovery column
    EmailTool-->>Agent: Email column created

    Agent->>User: "Confirm: Run email discovery for X prospects?"
    User-->>Agent: "Yes, proceed"

    Agent->>EmailTool: Execute email discovery
    EmailTool-->>Agent: Email discovery running

    Agent->>ResearchTool: Create research column
    ResearchTool->>Database: Setup research column
    Agent->>CopywriterTool: Create message column
    CopywriterTool->>Database: Setup copywriter column

    Agent->>User: "Email discovery complete. Research and messages ready to generate."
</div></code></pre>
<p><strong>Expected Tool Sequence:</strong></p>
<ol>
<li><code>upsert_work_email_column(column_name=&quot;Work Email&quot;, full_name=&quot;{{LinkedIn Profile.cell_details.full_name}}&quot;, company_domain=&quot;{{LinkedIn Company.cell_details.website}}&quot;)</code></li>
<li><code>run_column(column_id=&quot;email_column_id&quot;, count=50, wait_results=True)</code></li>
<li><code>upsert_bond_ai_researcher_column(column_name=&quot;Research Insights&quot;, linkedin_profile_url=&quot;{{LinkedIn Profile.cell_value}}&quot;)</code></li>
<li><code>upsert_ai_message_copywriter(column_name=&quot;Personalized Message&quot;, prompt=&quot;Write a personalized LinkedIn message...&quot;)</code></li>
</ol>
<h3 id="scenario-3-data-filtering-and-analysis">Scenario 3: Data Filtering and Analysis</h3>
<p><strong>User Query</strong>: &quot;Show me only prospects from companies with 100+ employees who haven't been contacted&quot;</p>
<pre><code class="language-mermaid"><div class="mermaid">sequenceDiagram
    participant User
    participant Agent
    participant FilterTool
    participant DataTool
    participant Database

    User->>Agent: "Filter for large companies, not contacted"
    Agent->>FilterTool: Read current filters
    FilterTool->>Database: Get table filters
    FilterTool-->>Agent: Current filter state

    Agent->>FilterTool: Update filters
    FilterTool->>Database: Apply new filter criteria
    FilterTool-->>Agent: Filters updated

    Agent->>DataTool: Read filtered data
    DataTool->>Database: Query with filters
    DataTool-->>Agent: Filtered dataset summary

    Agent->>User: "Showing X prospects from companies 100+ employees, not contacted"
</div></code></pre>
<p><strong>Expected Tool Sequence:</strong></p>
<ol>
<li><code>read_user_view_table_filters()</code></li>
<li><code>update_user_view_table_filters_tool(filters=FilterGroup(operator=&quot;AND&quot;, rules=[...]))</code></li>
<li><code>read_table_data(max_rows=10, summarize=True)</code></li>
</ol>
<h2 id="advanced-features">Advanced Features</h2>
<h3 id="smart-column-execution-with-confirmation">Smart Column Execution with Confirmation</h3>
<p>The agent implements intelligent confirmation for resource-intensive operations:</p>
<pre class="hljs"><code><div><span class="hljs-comment"># Example confirmation flow for run_column</span>
<span class="hljs-keyword">if</span> tool_call[<span class="hljs-string">"name"</span>] == <span class="hljs-string">"run_column"</span>:
    confirmation_message = {
        <span class="hljs-string">"column_name"</span>: column_name,
        <span class="hljs-string">"column_id"</span>: args.get(<span class="hljs-string">"column_id"</span>),
        <span class="hljs-string">"rows_count"</span>: count,
        <span class="hljs-string">"message"</span>: <span class="hljs-string">f"Do you want to run the column '<span class="hljs-subst">{column_name}</span>' for <span class="hljs-subst">{rows_text}</span>?"</span>
    }
    user_response = interrupt(confirmation_message)
</div></code></pre>
<h3 id="injection-path-system">Injection Path System</h3>
<p>The agent uses a sophisticated injection path system for data references:</p>
<p><strong>Basic Syntax:</strong></p>
<ul>
<li><code>{{Column Name.cell_value}}</code> - Direct cell value</li>
<li><code>{{Column Name.cell_details.field_name}}</code> - Nested field access</li>
<li><code>{{Column Name.cell_details.array_field.0.property}}</code> - Array element access</li>
</ul>
<p><strong>Examples:</strong></p>
<ul>
<li><code>{{LinkedIn Profile.cell_details.full_name}}</code> - Person's full name</li>
<li><code>{{LinkedIn Company.cell_details.website}}</code> - Company website</li>
<li><code>{{LinkedIn Profile.cell_details.experiences.0.company}}</code> - First job company</li>
</ul>
<h3 id="error-handling-patterns">Error Handling Patterns</h3>
<p><strong>Tool Execution Errors:</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">try</span>:
    tool_result = tool.invoke(tool_call[<span class="hljs-string">"args"</span>])
    outputs.append(ToolMessage(content=str(tool_result), ...))
<span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
    error_message = <span class="hljs-string">f"Error executing tool '<span class="hljs-subst">{tool_call[<span class="hljs-string">'name'</span>]}</span>': <span class="hljs-subst">{str(e)}</span>"</span>
    outputs.append(ToolMessage(content=error_message, ...))
</div></code></pre>
<p><strong>Model Call Fallbacks:</strong></p>
<pre class="hljs"><code><div><span class="hljs-keyword">try</span>:
    response = model.invoke(messages, config)
    <span class="hljs-keyword">return</span> {<span class="hljs-string">"messages"</span>: [response]}
<span class="hljs-keyword">except</span> Exception <span class="hljs-keyword">as</span> e:
    fallback_messages = [SystemMessage(<span class="hljs-string">f"Error: <span class="hljs-subst">{str(e)}</span>"</span>), *cleaned_messages[<span class="hljs-number">-3</span>:]]
    fallback_response = model.invoke(fallback_messages, config)
    <span class="hljs-keyword">return</span> {<span class="hljs-string">"messages"</span>: [fallback_response]}
</div></code></pre>
<h2 id="key-features">Key Features</h2>
<h3 id="intelligent-data-access">Intelligent Data Access</h3>
<ul>
<li>95% of operations use cached table summary instead of direct data queries</li>
<li>Automatic alignment with user's current table view (filters, sorts, searches)</li>
<li>Token-efficient summarization for large datasets</li>
</ul>
<h3 id="interactive-confirmations">Interactive Confirmations</h3>
<ul>
<li>User approval required for column execution operations</li>
<li>Clear communication of action scope (row counts, column names)</li>
<li>Graceful handling of user cancellations</li>
</ul>
<h3 id="error-resilience">Error Resilience</h3>
<ul>
<li>Comprehensive error handling at every layer</li>
<li>Automatic retries with fallback strategies</li>
<li>Detailed error reporting for debugging</li>
</ul>
<h3 id="mode-flexibility">Mode Flexibility</h3>
<ul>
<li><strong>Tool Mode</strong>: Full agent capabilities with tool access</li>
<li><strong>Chat Mode</strong>: Conversational guidance without tool execution</li>
<li>Dynamic mode switching based on user needs</li>
</ul>
<h2 id="performance-optimizations">Performance Optimizations</h2>
<h3 id="table-summary-caching">Table Summary Caching</h3>
<ul>
<li>Column descriptions cached in database to avoid repeated AI analysis</li>
<li>Fresh schema combined with stored summaries for optimal performance</li>
<li>Only processes columns lacking descriptions</li>
</ul>
<h3 id="efficient-data-queries">Efficient Data Queries</h3>
<ul>
<li>Summarization preferred over raw data retrieval</li>
<li>Targeted column selection when specific data needed</li>
<li>Respect for user's current view filters and sorts</li>
</ul>
<h3 id="stream-writing">Stream Writing</h3>
<ul>
<li>Real-time status updates for long-running operations</li>
<li>User feedback during tool execution</li>
<li>Progress tracking for multi-step workflows</li>
</ul>
<h2 id="implementation-details">Implementation Details</h2>
<h3 id="langgraph-configuration">LangGraph Configuration</h3>
<pre class="hljs"><code><div><span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">create_graph</span><span class="hljs-params">()</span>:</span>
    workflow = StateGraph(AgentState, config_schema=Configuration)

    <span class="hljs-comment"># Node definitions</span>
    workflow.add_node(<span class="hljs-string">"table_indexing"</span>, table_indexing_node)
    workflow.add_node(<span class="hljs-string">"agent"</span>, call_model)
    workflow.add_node(<span class="hljs-string">"tools"</span>, tool_node)

    <span class="hljs-comment"># Flow definition</span>
    workflow.set_entry_point(<span class="hljs-string">"table_indexing"</span>)
    workflow.add_edge(<span class="hljs-string">"table_indexing"</span>, <span class="hljs-string">"agent"</span>)
    workflow.add_conditional_edges(<span class="hljs-string">"agent"</span>, should_continue, {
        <span class="hljs-string">"continue"</span>: <span class="hljs-string">"tools"</span>,
        <span class="hljs-string">"end"</span>: END,
    })
    workflow.add_edge(<span class="hljs-string">"tools"</span>, <span class="hljs-string">"agent"</span>)

    <span class="hljs-keyword">return</span> workflow.compile()
</div></code></pre>
<h3 id="state-reducers">State Reducers</h3>
<p><strong>Message Aggregation:</strong></p>
<pre class="hljs"><code><div>messages: Annotated[Sequence[BaseMessage], add_messages]
</div></code></pre>
<ul>
<li>Automatically combines new messages with conversation history</li>
<li>Maintains chronological order</li>
<li>Handles different message types (Human, AI, Tool, System)</li>
</ul>
<p><strong>Table Summary Preservation:</strong></p>
<pre class="hljs"><code><div><span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">preserve_table_summary</span><span class="hljs-params">(left: Optional[str], right: Optional[str])</span> -&gt; Optional[str]:</span>
    <span class="hljs-keyword">if</span> right <span class="hljs-keyword">is</span> <span class="hljs-keyword">not</span> <span class="hljs-literal">None</span>:
        <span class="hljs-keyword">return</span> right
    <span class="hljs-keyword">return</span> left
</div></code></pre>
<h3 id="tool-registration-pattern">Tool Registration Pattern</h3>
<pre class="hljs"><code><div>tools = [search, scrape_website, upsert_linkedin_person_profile_column_from_url, ...]
tools_by_name = {tool.name: tool <span class="hljs-keyword">for</span> tool <span class="hljs-keyword">in</span> tools}
</div></code></pre>
<h2 id="best-practices-and-guidelines">Best Practices and Guidelines</h2>
<h3 id="tool-development">Tool Development</h3>
<ol>
<li><strong>Consistent Error Handling</strong>: All tools return <code>Tuple[Optional[str], Optional[str]]</code></li>
<li><strong>Stream Writing</strong>: Provide user feedback for long operations</li>
<li><strong>Validation</strong>: Use Pydantic models for input validation</li>
<li><strong>Configuration Injection</strong>: Access table_id and settings via <code>RunnableConfig</code></li>
</ol>
<h3 id="prompt-engineering">Prompt Engineering</h3>
<ol>
<li><strong>Context Awareness</strong>: Include table summary and current filters</li>
<li><strong>Mode Differentiation</strong>: Separate instructions for chat vs tool mode</li>
<li><strong>Selected Data Context</strong>: Highlight user-selected rows/columns</li>
<li><strong>Error Recovery</strong>: Provide fallback prompts for error scenarios</li>
</ol>
<h3 id="state-management">State Management</h3>
<ol>
<li><strong>Minimal State</strong>: Only store essential information in state</li>
<li><strong>Reducer Functions</strong>: Use appropriate reducers for state updates</li>
<li><strong>Type Safety</strong>: Leverage TypedDict for state structure</li>
<li><strong>Immutability</strong>: Treat state as immutable, return updates</li>
</ol>
<h3 id="performance-considerations">Performance Considerations</h3>
<ol>
<li><strong>Lazy Loading</strong>: Load data only when needed</li>
<li><strong>Caching Strategy</strong>: Cache expensive operations (table summaries)</li>
<li><strong>Batch Operations</strong>: Group related operations when possible</li>
<li><strong>Resource Management</strong>: Clean up resources after tool execution</li>
</ol>
<h2 id="security-and-data-protection">Security and Data Protection</h2>
<h3 id="sensitive-data-handling">Sensitive Data Handling</h3>
<ul>
<li>Exclude <code>run_status</code> fields from AI analysis</li>
<li>Validate injection sequences before execution</li>
<li>Sanitize user inputs in tool parameters</li>
<li>Implement proper access controls for table operations</li>
</ul>
<h3 id="api-key-management">API Key Management</h3>
<ul>
<li>Environment variable configuration for external services</li>
<li>Graceful degradation when services unavailable</li>
<li>Error messages that don't expose sensitive information</li>
</ul>
<h2 id="monitoring-and-debugging">Monitoring and Debugging</h2>
<h3 id="logging-strategy">Logging Strategy</h3>
<ul>
<li>Comprehensive error logging with context</li>
<li>Tool execution tracking</li>
<li>Performance metrics for optimization</li>
<li>User interaction patterns</li>
</ul>
<h3 id="debug-information">Debug Information</h3>
<ul>
<li>State transitions logged</li>
<li>Tool call parameters and results</li>
<li>Model response analysis</li>
<li>Error stack traces with sanitization</li>
</ul>
<h2 id="extension-points">Extension Points</h2>
<h3 id="adding-new-tools">Adding New Tools</h3>
<ol>
<li>Create tool function with proper type annotations</li>
<li>Add to tools list in <code>tools.py</code></li>
<li>Update documentation and prompts</li>
<li>Implement proper error handling and validation</li>
</ol>
<h3 id="custom-node-types">Custom Node Types</h3>
<ol>
<li>Define node function with <code>(state: AgentState, config: RunnableConfig)</code> signature</li>
<li>Add to workflow with <code>workflow.add_node()</code></li>
<li>Define appropriate edges and conditions</li>
<li>Test state transitions thoroughly</li>
</ol>
<h3 id="integration-patterns">Integration Patterns</h3>
<ol>
<li><strong>Database Integration</strong>: Use <code>agent_db.py</code> functions</li>
<li><strong>External APIs</strong>: Implement with proper error handling</li>
<li><strong>AI Model Integration</strong>: Use utility functions from <code>utils.py</code></li>
<li><strong>Validation Services</strong>: Leverage existing validation patterns</li>
</ol>
<p>This documentation provides a comprehensive technical overview of the Outbond AI Assistant implementation, covering architecture, workflows, tools, and best practices for development and maintenance.</p>

</body>
</html>
